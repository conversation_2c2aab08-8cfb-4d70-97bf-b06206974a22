"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  Trophy, 
  Target, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  RotateCcw,
  Share2,
  Download,
  ArrowLeft,
  Star,
  TrendingUp,
  Award,
  BookOpen,
  Eye,
  Flag
} from "lucide-react"
import { motion } from "framer-motion"
import { toast } from "sonner"
import Link from "next/link"
import { QuestionRenderer } from "@/components/student/question-renderer"
import { QuizResultExportButton } from "@/components/pdf/pdf-export-button"

interface QuizResult {
  id: string
  quizId: string
  quiz: {
    title: string
    description: string
    type: string
    difficulty: string
    duration: number
    passingScore?: number
    totalPoints: number
  }
  student: {
    name: string
    email: string
  }
  score: number
  percentage: number
  totalQuestions: number
  correctAnswers: number
  incorrectAnswers: number
  unansweredQuestions: number
  timeSpent: number
  startedAt: string
  completedAt: string
  passed: boolean
  rank?: number
  totalAttempts?: number
  questions: Array<{
    id: string
    type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'ESSAY' | 'MATCHING' | 'SHORT_ANSWER'
    text: string
    options?: string[]
    correctAnswer?: string
    explanation?: string
    points: number
    difficulty: 'EASY' | 'MEDIUM' | 'HARD'
    tags: string[]
    studentAnswer: any
    isCorrect: boolean
    pointsEarned: number
  }>
  feedback?: string
  recommendations?: string[]
}

export default function QuizResult() {
  const params = useParams()
  const router = useRouter()
  const [result, setResult] = useState<QuizResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0)

  useEffect(() => {
    fetchQuizResult()
  }, [params.id, params.attemptId])

  const fetchQuizResult = async (isRetry = false) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/student/quiz/${params.id}/result/${params.attemptId}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Quiz result not found. Please check if the quiz was completed.')
        } else if (response.status >= 500) {
          throw new Error('Server error. Please try again in a moment.')
        } else {
          throw new Error('Failed to fetch quiz results')
        }
      }

      const data = await response.json()

      if (data.success) {
        // API now returns data in the exact format expected by frontend
        setResult(data.data)
        setRetryCount(0) // Reset retry count on success
      } else {
        throw new Error(data.message || 'Failed to fetch quiz results')
      }
    } catch (error: any) {
      console.error('Error fetching quiz result:', error)
      setError(error.message || 'Failed to load quiz result')

      if (!isRetry) {
        toast.error(error.message || 'Failed to load quiz result')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    fetchQuizResult(true)
  }

  const handleRetakeQuiz = () => {
    router.push(`/student/quiz/${params.id}`)
  }

  const handleShareResult = () => {
    if (navigator.share) {
      navigator.share({
        title: `Quiz Result: ${result?.quiz.title}`,
        text: `I scored ${result?.percentage}% on ${result?.quiz.title}!`,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Result link copied to clipboard!')
    }
  }

  const getScoreColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 70) return 'text-blue-600'
    if (percentage >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getPerformanceLevel = (percentage: number) => {
    if (percentage >= 90) return { level: 'Excellent', color: 'bg-green-500', icon: Trophy }
    if (percentage >= 70) return { level: 'Good', color: 'bg-blue-500', icon: Target }
    if (percentage >= 50) return { level: 'Average', color: 'bg-yellow-500', icon: AlertCircle }
    return { level: 'Needs Improvement', color: 'bg-red-500', icon: XCircle }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading results...</p>
        </div>
      </div>
    )
  }

  if (error && !loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <XCircle className="h-16 w-16 text-destructive mx-auto" />
              <h3 className="text-xl font-semibold">Failed to Load Result</h3>
              <p className="text-muted-foreground">
                {error}
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={handleRetry} variant="default">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button asChild variant="outline">
                  <Link href="/student/browse">
                    Back to Browse
                  </Link>
                </Button>
              </div>
              {retryCount > 0 && (
                <p className="text-xs text-muted-foreground">
                  Retry attempt: {retryCount}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!result) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <XCircle className="h-16 w-16 text-destructive mx-auto" />
              <h3 className="text-xl font-semibold">Result Not Found</h3>
              <p className="text-muted-foreground">
                Unable to load the quiz result. Please try again later.
              </p>
              <Button asChild>
                <Link href="/student/browse">
                  Back to Browse
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const performance = getPerformanceLevel(result.percentage)
  const PerformanceIcon = performance.icon

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/student/browse">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Browse
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Quiz Results</h1>
              <p className="text-muted-foreground mt-1">
                {result.quiz.title}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleShareResult}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <QuizResultExportButton
              attemptId={params.attemptId as string}
              variant="outline"
              size="default"
            />
          </div>
        </div>

        {/* Score Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="relative overflow-hidden">
            <div className={`absolute inset-0 ${performance.color} opacity-5`}></div>
            <CardContent className="pt-8 pb-8">
              <div className="text-center space-y-6">
                <div className="flex items-center justify-center">
                  <div className={`w-20 h-20 rounded-full ${performance.color} flex items-center justify-center`}>
                    <PerformanceIcon className="h-10 w-10 text-white" />
                  </div>
                </div>
                
                <div>
                  <div className={`text-6xl font-bold ${getScoreColor(result.percentage)} mb-2`}>
                    {result.percentage}%
                  </div>
                  <div className="text-xl font-semibold text-muted-foreground">
                    {performance.level}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    {result.score} out of {result.quiz.totalPoints} points
                  </div>
                  {result.quiz.passingScore && result.quiz.passingScore > 0 && (
                    <div className="text-sm text-muted-foreground">
                      Passing Score: {result.quiz.passingScore}% •
                      <span className={result.passed ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                        {result.passed ? ' Achieved' : ' Not Achieved'}
                      </span>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-center gap-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{result.correctAnswers}</div>
                    <div className="text-sm text-muted-foreground">Correct</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{result.incorrectAnswers}</div>
                    <div className="text-sm text-muted-foreground">Incorrect</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{result.unansweredQuestions}</div>
                    <div className="text-sm text-muted-foreground">Unanswered</div>
                  </div>
                </div>

                {result.passed ? (
                  <Badge className="bg-green-500 text-lg px-4 py-2">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Passed
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="text-lg px-4 py-2">
                    <XCircle className="h-4 w-4 mr-2" />
                    Failed
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Detailed Results */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Statistics */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Time Spent</span>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="font-semibold">{result.timeSpent} min</span>
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Questions</span>
                  <span className="font-semibold">{result.totalQuestions}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Accuracy</span>
                  <span className="font-semibold">{Math.round((result.correctAnswers / result.totalQuestions) * 100)}%</span>
                </div>
                
                {result.rank && result.totalAttempts && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Rank</span>
                    <div className="flex items-center gap-1">
                      <Award className="h-4 w-4 text-yellow-500" />
                      <span className="font-semibold">#{result.rank} of {result.totalAttempts}</span>
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <div className="flex justify-between text-sm mb-2">
                    <span>Overall Progress</span>
                    <span>{result.percentage}%</span>
                  </div>
                  <Progress value={result.percentage} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Next Steps</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button onClick={handleRetakeQuiz} className="w-full">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Retake Quiz
                </Button>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/student/browse">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Browse More Quizzes
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/student/analytics">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Analytics
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Feedback */}
            {result.feedback && (
              <Card>
                <CardHeader>
                  <CardTitle>Feedback</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {result.feedback}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Question Review */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="review" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="review">Question Review</TabsTrigger>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
              </TabsList>

              <TabsContent value="review" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      Review Your Answers
                    </CardTitle>
                    <CardDescription>
                      Go through each question to understand your performance
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* Question Navigation */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {result.questions.map((question, index) => (
                        <Button
                          key={question.id}
                          variant={selectedQuestionIndex === index ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedQuestionIndex(index)}
                          className={`${
                            question.isCorrect 
                              ? 'border-green-500 text-green-600 hover:bg-green-50' 
                              : 'border-red-500 text-red-600 hover:bg-red-50'
                          }`}
                        >
                          <div className="flex items-center gap-1">
                            {question.isCorrect ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <XCircle className="h-3 w-3" />
                            )}
                            {index + 1}
                          </div>
                        </Button>
                      ))}
                    </div>

                    {/* Selected Question */}
                    {result.questions[selectedQuestionIndex] && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold">
                            Question {selectedQuestionIndex + 1}
                          </h3>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              {result.questions[selectedQuestionIndex].pointsEarned} / {result.questions[selectedQuestionIndex].points} points
                            </Badge>
                            {result.questions[selectedQuestionIndex].isCorrect ? (
                              <Badge className="bg-green-500">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Correct
                              </Badge>
                            ) : (
                              <Badge variant="destructive">
                                <XCircle className="h-3 w-3 mr-1" />
                                Incorrect
                              </Badge>
                            )}
                          </div>
                        </div>

                        <QuestionRenderer
                          question={result.questions[selectedQuestionIndex]}
                          answer={result.questions[selectedQuestionIndex].studentAnswer}
                          onAnswerChange={() => {}}
                          disabled={true}
                          showExplanation={true}
                          showCorrectAnswer={true}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="recommendations">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5" />
                      Recommendations
                    </CardTitle>
                    <CardDescription>
                      Suggested areas for improvement based on your performance
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {result.recommendations && result.recommendations.length > 0 ? (
                      <ul className="space-y-3">
                        {result.recommendations.map((recommendation, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-0.5">
                              <span className="text-xs font-bold text-primary">{index + 1}</span>
                            </div>
                            <span className="text-sm leading-relaxed">{recommendation}</span>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-muted-foreground">
                        Great job! No specific recommendations at this time. Keep up the excellent work!
                      </p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
