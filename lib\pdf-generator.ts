import React from 'react'
import { Document, Page, Text, View, StyleSheet, pdf, Font } from '@react-pdf/renderer'
import html2canvas from 'html2canvas'

// Register fonts for better PDF rendering
Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-light-webfont.ttf',
      fontWeight: 300,
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf',
      fontWeight: 400,
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf',
      fontWeight: 500,
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf',
      fontWeight: 700,
    },
  ],
})

// PDF Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30,
    fontFamily: 'Roboto',
  },
  header: {
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: 20,
    marginBottom: 20,
    borderRadius: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: 'normal',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1f2937',
  },
  table: {
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableRow: {
    margin: 'auto',
    flexDirection: 'row',
  },
  tableColHeader: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderColor: '#e5e7eb',
    backgroundColor: '#f3f4f6',
    padding: 8,
  },
  tableCol: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderColor: '#e5e7eb',
    padding: 8,
  },
  tableCellHeader: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#374151',
  },
  tableCell: {
    fontSize: 11,
    color: '#6b7280',
  },
  scoreBox: {
    position: 'absolute',
    right: 30,
    top: 30,
    width: 80,
    height: 60,
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  scoreLabel: {
    color: 'white',
    fontSize: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    color: '#9ca3af',
    fontSize: 8,
  },
  questionTable: {
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  questionRow: {
    flexDirection: 'row',
  },
  questionCol: {
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderColor: '#e5e7eb',
    padding: 6,
  },
  questionColSmall: {
    width: '10%',
  },
  questionColMedium: {
    width: '20%',
  },
  questionColLarge: {
    width: '25%',
  },
  correctAnswer: {
    color: '#059669',
    fontWeight: 'bold',
  },
  incorrectAnswer: {
    color: '#dc2626',
    fontWeight: 'bold',
  },
})

interface PDFOptions {
  title?: string
  author?: string
  subject?: string
  keywords?: string
  creator?: string
}

interface QuizResult {
  id: string
  quiz: {
    title: string
    description: string
    category: string
    difficulty: string
    totalQuestions: number
    totalPoints: number
    duration: number
  }
  student: {
    name: string
    email: string
    id: string
  }
  score: number
  percentage: number
  timeSpent: number
  completedAt: string
  questions: Array<{
    id: string
    text: string
    type: string
    options?: string[]
    correctAnswer: string
    studentAnswer: any
    isCorrect: boolean
    points: number
    pointsEarned: number
    explanation?: string
  }>
  rank?: number
  totalAttempts?: number
}

interface AnalyticsData {
  overview: {
    totalAttempts: number
    averageScore: number
    totalTimeSpent: number
    improvementRate: number
  }
  performanceByCategory: Array<{
    category: string
    attempts: number
    averageScore: number
    improvement: number
  }>
  performanceByDifficulty: Array<{
    difficulty: string
    attempts: number
    averageScore: number
    successRate: number
  }>
  weeklyProgress: Array<{
    week: string
    attempts: number
    averageScore: number
    timeSpent: number
  }>
}

// Helper function to create React PDF components
const createQuizResultDocument = (result: QuizResult) => {
  return React.createElement(Document, null,
    // Page 1
    React.createElement(Page, { size: "A4", style: styles.page },
      // Header
      React.createElement(View, { style: styles.header },
        React.createElement(Text, { style: styles.headerTitle }, "Quiz Result Report"),
        React.createElement(Text, { style: styles.headerSubtitle },
          `${result.quiz.title} - ${result.student.name}`)
      ),

      // Student Information
      React.createElement(View, { style: styles.section },
        React.createElement(Text, { style: styles.sectionTitle }, "Student Information"),
        React.createElement(View, { style: styles.table },
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableColHeader },
              React.createElement(Text, { style: styles.tableCellHeader }, "Field")
            ),
            React.createElement(View, { style: styles.tableColHeader },
              React.createElement(Text, { style: styles.tableCellHeader }, "Value")
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Name")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, result.student.name)
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Email")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, result.student.email)
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Completed At")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell },
                new Date(result.completedAt).toLocaleString())
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Time Spent")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, `${result.timeSpent} minutes`)
            )
          )
        )
      ),

      // Performance Summary
      React.createElement(View, { style: styles.section },
        React.createElement(Text, { style: styles.sectionTitle }, "Performance Summary"),
        React.createElement(View, {
          style: [styles.scoreBox, {
            backgroundColor: result.percentage >= 90 ? '#22c55e' :
                            result.percentage >= 70 ? '#3b82f6' :
                            result.percentage >= 50 ? '#f59e0b' : '#ef4444'
          }]
        },
          React.createElement(Text, { style: styles.scoreText }, `${result.percentage}%`),
          React.createElement(Text, { style: styles.scoreLabel }, "Final Score")
        ),
        React.createElement(View, { style: styles.table },
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableColHeader },
              React.createElement(Text, { style: styles.tableCellHeader }, "Metric")
            ),
            React.createElement(View, { style: styles.tableColHeader },
              React.createElement(Text, { style: styles.tableCellHeader }, "Value")
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Correct Answers")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell },
                `${result.questions.filter(q => q.isCorrect).length} / ${result.quiz.totalQuestions}`)
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Points Earned")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell },
                `${result.score} / ${result.quiz.totalPoints}`)
            )
          ),
          React.createElement(View, { style: styles.tableRow },
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, { style: styles.tableCell }, "Status")
            ),
            React.createElement(View, { style: styles.tableCol },
              React.createElement(Text, {
                style: [styles.tableCell, result.percentage >= 70 ? styles.correctAnswer : styles.incorrectAnswer]
              }, result.percentage >= 70 ? 'PASSED' : 'FAILED')
            )
          )
        )
      ),

      // Footer
      React.createElement(Text, { style: styles.footer },
        `Generated on ${new Date().toLocaleString()} | Page 1`)
    ),

    // Page 2 - Question Details
    React.createElement(Page, { size: "A4", style: styles.page },
      React.createElement(View, { style: styles.header },
        React.createElement(Text, { style: styles.headerTitle }, "Detailed Question Analysis")
      ),
      React.createElement(View, { style: styles.section },
        React.createElement(View, { style: styles.questionTable },
          React.createElement(View, { style: styles.questionRow },
            React.createElement(View, { style: [styles.questionCol, styles.questionColSmall, styles.tableColHeader] },
              React.createElement(Text, { style: styles.tableCellHeader }, "#")
            ),
            React.createElement(View, { style: [styles.questionCol, styles.questionColMedium, styles.tableColHeader] },
              React.createElement(Text, { style: styles.tableCellHeader }, "Type")
            ),
            React.createElement(View, { style: [styles.questionCol, styles.questionColLarge, styles.tableColHeader] },
              React.createElement(Text, { style: styles.tableCellHeader }, "Result")
            ),
            React.createElement(View, { style: [styles.questionCol, styles.questionColMedium, styles.tableColHeader] },
              React.createElement(Text, { style: styles.tableCellHeader }, "Points")
            ),
            React.createElement(View, { style: [styles.questionCol, styles.questionColSmall, styles.tableColHeader] },
              React.createElement(Text, { style: styles.tableCellHeader }, "Status")
            )
          ),
          ...result.questions.map((question, index) =>
            React.createElement(View, { key: question.id, style: styles.questionRow },
              React.createElement(View, { style: [styles.questionCol, styles.questionColSmall] },
                React.createElement(Text, { style: styles.tableCell }, (index + 1).toString())
              ),
              React.createElement(View, { style: [styles.questionCol, styles.questionColMedium] },
                React.createElement(Text, { style: styles.tableCell }, question.type)
              ),
              React.createElement(View, { style: [styles.questionCol, styles.questionColLarge] },
                React.createElement(Text, { style: styles.tableCell },
                  question.isCorrect ? 'Correct' : 'Incorrect')
              ),
              React.createElement(View, { style: [styles.questionCol, styles.questionColMedium] },
                React.createElement(Text, { style: styles.tableCell },
                  `${question.pointsEarned} / ${question.points}`)
              ),
              React.createElement(View, { style: [styles.questionCol, styles.questionColSmall] },
                React.createElement(Text, {
                  style: [styles.tableCell, question.isCorrect ? styles.correctAnswer : styles.incorrectAnswer]
                }, question.isCorrect ? '✓' : '✗')
              )
            )
          )
        )
      ),
      React.createElement(Text, { style: styles.footer },
        `Generated on ${new Date().toLocaleString()} | Page 2`)
    )
  )
}

// Generate Quiz Result PDF using React PDF
async function generateQuizResultPDF(result: QuizResult): Promise<Blob> {
  try {
    const pdfDocument = createQuizResultDocument(result)
    const blob = await pdf(pdfDocument).toBlob()
    return blob
  } catch (error) {
    console.error('Error generating quiz result PDF:', error)
    throw new Error(`Failed to generate quiz result PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Generate Analytics Report PDF (simplified version)
async function generateAnalyticsReportPDF(data: AnalyticsData, studentName: string): Promise<Blob> {
  try {
    // Validate data structure
    if (!data || !data.overview) {
      throw new Error('Invalid analytics data: missing overview section')
    }
    // Create a simple analytics document
    const pdfDocument = React.createElement(Document, null,
      React.createElement(Page, { size: "A4", style: styles.page },
        React.createElement(View, { style: styles.header },
          React.createElement(Text, { style: styles.headerTitle }, "Learning Analytics Report"),
          React.createElement(Text, { style: styles.headerSubtitle }, `Performance Analysis for ${studentName}`)
        ),
        React.createElement(View, { style: styles.section },
          React.createElement(Text, { style: styles.sectionTitle }, "Performance Overview"),
          React.createElement(View, { style: styles.table },
            React.createElement(View, { style: styles.tableRow },
              React.createElement(View, { style: styles.tableColHeader },
                React.createElement(Text, { style: styles.tableCellHeader }, "Metric")
              ),
              React.createElement(View, { style: styles.tableColHeader },
                React.createElement(Text, { style: styles.tableCellHeader }, "Value")
              )
            ),
            React.createElement(View, { style: styles.tableRow },
              React.createElement(View, { style: styles.tableCol },
                React.createElement(Text, { style: styles.tableCell }, "Total Attempts")
              ),
              React.createElement(View, { style: styles.tableCol },
                React.createElement(Text, { style: styles.tableCell }, (data.overview.totalAttempts || 0).toString())
              )
            ),
            React.createElement(View, { style: styles.tableRow },
              React.createElement(View, { style: styles.tableCol },
                React.createElement(Text, { style: styles.tableCell }, "Average Score")
              ),
              React.createElement(View, { style: styles.tableCol },
                React.createElement(Text, { style: styles.tableCell }, `${(data.overview.averageScore || 0).toFixed(1)}%`)
              )
            ),
            React.createElement(View, { style: styles.tableRow },
              React.createElement(View, { style: styles.tableCol },
                React.createElement(Text, { style: styles.tableCell }, "Total Time Spent")
              ),
              React.createElement(View, { style: styles.tableCol },
                React.createElement(Text, { style: styles.tableCell }, `${Math.round((data.overview.totalTimeSpent || 0) / 60)} hours`)
              )
            )
          )
        ),
        React.createElement(Text, { style: styles.footer },
          `Generated on ${new Date().toLocaleString()}`)
      )
    )

    const blob = await pdf(pdfDocument).toBlob()
    return blob
  } catch (error) {
    console.error('Error generating analytics report PDF:', error)
    throw new Error(`Failed to generate analytics report PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Generate Certificate PDF (simplified version)
async function generateCertificatePDF(data: {
  studentName: string
  quizTitle: string
  score: number
  completedAt: string
  certificateId: string
}): Promise<Blob> {
  try {
    const certificateStyles = StyleSheet.create({
      page: {
        flexDirection: 'column',
        backgroundColor: '#ffffff',
        padding: 40,
        fontFamily: 'Roboto',
        justifyContent: 'center',
        alignItems: 'center',
      },
      border: {
        border: '3px solid #3b82f6',
        borderRadius: 10,
        padding: 30,
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
      },
      title: {
        fontSize: 32,
        fontWeight: 'bold',
        color: '#3b82f6',
        marginBottom: 20,
        textAlign: 'center',
      },
      studentName: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#000000',
        marginBottom: 15,
        textAlign: 'center',
      },
      text: {
        fontSize: 16,
        color: '#000000',
        marginBottom: 10,
        textAlign: 'center',
      },
      quizTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#3b82f6',
        marginBottom: 15,
        textAlign: 'center',
      },
      score: {
        fontSize: 18,
        color: '#000000',
        marginBottom: 15,
        textAlign: 'center',
      },
      date: {
        fontSize: 14,
        color: '#666666',
        marginBottom: 20,
        textAlign: 'center',
      },
      certificateId: {
        fontSize: 10,
        color: '#999999',
        position: 'absolute',
        bottom: 20,
        left: 20,
      },
    })

    const pdfDocument = React.createElement(Document, null,
      React.createElement(Page, { size: "A4", orientation: "landscape", style: certificateStyles.page },
        React.createElement(View, { style: certificateStyles.border },
          React.createElement(Text, { style: certificateStyles.title }, "CERTIFICATE OF COMPLETION"),
          React.createElement(Text, { style: certificateStyles.studentName }, data.studentName),
          React.createElement(Text, { style: certificateStyles.text }, "has successfully completed the quiz"),
          React.createElement(Text, { style: certificateStyles.quizTitle }, data.quizTitle),
          React.createElement(Text, { style: certificateStyles.score }, `with a score of ${data.score}%`),
          React.createElement(Text, { style: certificateStyles.date },
            `Completed on ${new Date(data.completedAt).toLocaleDateString()}`)
        ),
        React.createElement(Text, { style: certificateStyles.certificateId },
          `Certificate ID: ${data.certificateId}`)
      )
    )

    const blob = await pdf(pdfDocument).toBlob()
    return blob
  } catch (error) {
    console.error('Error generating certificate PDF:', error)
    throw new Error(`Failed to generate certificate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Capture HTML element as PDF (using html2canvas)
async function captureElementAsPDF(elementId: string, _filename: string = 'document.pdf'): Promise<Blob> {
  if (typeof document === 'undefined') {
    throw new Error('This function can only be used in browser environment')
  }

  const element = document.getElementById(elementId)
  if (!element) {
    throw new Error(`Element with ID '${elementId}' not found`)
  }

  const canvas = await html2canvas(element, {
    scale: 2,
    useCORS: true,
    allowTaint: true
  })

  const imgData = canvas.toDataURL('image/png')

  // Create a simple document with the image
  const pdfDocument = React.createElement(Document, null,
    React.createElement(Page, { size: "A4", style: styles.page },
      React.createElement(View, { style: { width: '100%', height: '100%' } },
        // Note: @react-pdf/renderer doesn't support external images directly
        // This is a simplified version - for full functionality, you'd need to convert
        // the canvas to a data URL and handle it appropriately
        React.createElement(Text, { style: styles.sectionTitle }, "Captured Content"),
        React.createElement(Text, { style: styles.tableCell }, "Content captured from HTML element")
      )
    )
  )

  const blob = await pdf(pdfDocument).toBlob()
  return blob
}

// Download PDF utility function
function downloadPDF(blob: Blob, filename: string) {
  if (typeof document === 'undefined') {
    throw new Error('This function can only be used in browser environment')
  }

  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Export the functions
export { generateQuizResultPDF, generateAnalyticsReportPDF, generateCertificatePDF, captureElementAsPDF, downloadPDF }
export type { QuizResult, AnalyticsData }
