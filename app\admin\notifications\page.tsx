"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { 
  Bell, 
  Send, 
  Users, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  AlertTriangle,
  Info,
  MessageSquare,
  Calendar,
  Settings
} from "lucide-react"
import { NotificationManager } from "@/components/admin/notification-manager"
import { toast } from "sonner"

interface NotificationStats {
  totalSent: number
  totalDelivered: number
  totalRead: number
  totalClicked: number
  deliveryRate: number
  readRate: number
  clickRate: number
}

interface RecentNotification {
  id: string
  type: string
  title: string
  message: string
  priority: string
  sentAt: Date
  recipientCount: number
  readCount: number
  clickCount: number
}

export default function NotificationsPage() {
  const [stats, setStats] = useState<NotificationStats>({
    totalSent: 0,
    totalDelivered: 0,
    totalRead: 0,
    totalClicked: 0,
    deliveryRate: 0,
    readRate: 0,
    clickRate: 0
  })
  const [recentNotifications, setRecentNotifications] = useState<RecentNotification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [period, setPeriod] = useState<'7d' | '30d' | '90d' | 'all'>('30d')

  useEffect(() => {
    loadNotificationData()
  }, [period])

  const loadNotificationData = async () => {
    try {
      setIsLoading(true)
      
      const response = await fetch(`/api/admin/notifications/stats?period=${period}`)
      if (!response.ok) throw new Error('Failed to fetch notification stats')

      const data = await response.json()
      const statsData = data.data

      setStats({
        totalSent: statsData.overview.totalSent,
        totalDelivered: statsData.overview.totalDelivered,
        totalRead: statsData.overview.totalRead,
        totalClicked: statsData.overview.totalClicked,
        deliveryRate: statsData.overview.deliveryRate,
        readRate: statsData.overview.readRate,
        clickRate: statsData.overview.clickRate
      })

      setRecentNotifications(statsData.recentNotifications.map((notification: any) => ({
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        priority: notification.priority,
        sentAt: new Date(notification.sentAt),
        recipientCount: notification.recipientCount,
        readCount: notification.readCount,
        clickCount: notification.clickCount
      })))
    } catch (error) {
      console.error('Error loading notification data:', error)
      toast.error('Failed to load notification data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadNotificationData()
    setRefreshing(false)
    toast.success('Notification data refreshed')
  }

  const sendQuickNotification = async (type: string, title: string, message: string) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          title,
          message,
          sendToAll: true,
          priority: 'normal'
        })
      })

      if (response.ok) {
        toast.success('Quick notification sent successfully!')
        loadNotificationData() // Refresh data
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to send notification')
      }
    } catch (error) {
      console.error('Error sending quick notification:', error)
      toast.error('Failed to send notification')
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500'
      case 'high': return 'bg-orange-500'
      case 'normal': return 'bg-blue-500'
      case 'low': return 'bg-gray-500'
      default: return 'bg-blue-500'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUIZ_AVAILABLE': return <CheckCircle className="h-4 w-4" />
      case 'ANNOUNCEMENT': return <Bell className="h-4 w-4" />
      case 'SYSTEM_UPDATE': return <Info className="h-4 w-4" />
      case 'MAINTENANCE': return <AlertTriangle className="h-4 w-4" />
      case 'ACHIEVEMENT_UNLOCKED': return <TrendingUp className="h-4 w-4" />
      default: return <MessageSquare className="h-4 w-4" />
    }
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bell className="h-8 w-8 text-blue-600" />
            Notification Management
          </h1>
          <p className="text-muted-foreground mt-1">
            Send and manage notifications across the platform
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value as '7d' | '30d' | '90d' | 'all')}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="all">All time</option>
          </select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <Bell className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats.totalSent.toLocaleString()
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Total Sent</p>
              </div>
              <Send className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    `${stats.deliveryRate}%`
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Delivery Rate</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    `${stats.readRate}%`
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Read Rate</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {isLoading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    `${stats.clickRate}%`
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Click Rate</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="send" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="send">Send Notification</TabsTrigger>
          <TabsTrigger value="recent">Recent Notifications</TabsTrigger>
          <TabsTrigger value="templates">Quick Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Send Notification Tab */}
        <TabsContent value="send">
          <NotificationManager />
        </TabsContent>

        {/* Recent Notifications Tab */}
        <TabsContent value="recent" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Notifications</CardTitle>
              <CardDescription>
                View and manage recently sent notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  // Loading skeleton
                  [...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                      <div className="flex items-center gap-4">
                        <div className="w-6 h-6 bg-muted rounded"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-muted rounded w-48 mb-2"></div>
                          <div className="h-3 bg-muted rounded w-64 mb-2"></div>
                          <div className="h-3 bg-muted rounded w-32"></div>
                        </div>
                      </div>
                      <div className="h-6 w-16 bg-muted rounded"></div>
                    </div>
                  ))
                ) : recentNotifications.length === 0 ? (
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No notifications found</h3>
                    <p className="text-muted-foreground">
                      No notifications have been sent in the selected time period.
                    </p>
                  </div>
                ) : (
                  recentNotifications.map((notification) => (
                  <div key={notification.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(notification.type)}
                        <div className={`w-2 h-2 rounded-full ${getPriorityColor(notification.priority)}`} />
                      </div>
                      
                      <div className="flex-1">
                        <h4 className="font-medium">{notification.title}</h4>
                        <p className="text-sm text-muted-foreground">{notification.message}</p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span>Sent: {notification.sentAt.toLocaleString()}</span>
                          <span>Recipients: {notification.recipientCount}</span>
                          <span>Read: {notification.readCount}</span>
                          <span>Clicked: {notification.clickCount}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {Math.round((notification.readCount / notification.recipientCount) * 100)}% read
                      </Badge>
                    </div>
                  </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Quick Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Maintenance</CardTitle>
                <CardDescription>
                  Notify users about scheduled maintenance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  className="w-full"
                  onClick={() => sendQuickNotification(
                    'MAINTENANCE',
                    'Scheduled Maintenance',
                    'We will be performing system maintenance tonight from 2-4 AM EST. Some features may be temporarily unavailable.'
                  )}
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Send Maintenance Notice
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">New Feature</CardTitle>
                <CardDescription>
                  Announce new platform features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  className="w-full"
                  onClick={() => sendQuickNotification(
                    'SYSTEM_UPDATE',
                    'New Feature Available!',
                    'We\'ve added exciting new features to enhance your learning experience. Check them out now!'
                  )}
                >
                  <Info className="h-4 w-4 mr-2" />
                  Announce Feature
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">General Announcement</CardTitle>
                <CardDescription>
                  Send general announcements to all users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  className="w-full"
                  onClick={() => sendQuickNotification(
                    'ANNOUNCEMENT',
                    'Important Announcement',
                    'We have an important update to share with our community. Please check your dashboard for more details.'
                  )}
                >
                  <Bell className="h-4 w-4 mr-2" />
                  Send Announcement
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Engagement Metrics</CardTitle>
                <CardDescription>
                  Notification performance over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Delivery Rate</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-muted rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${stats.deliveryRate}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{stats.deliveryRate}%</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Read Rate</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-muted rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ width: `${stats.readRate}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{stats.readRate}%</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Click Rate</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-muted rounded-full h-2">
                        <div 
                          className="bg-orange-500 h-2 rounded-full" 
                          style={{ width: `${stats.clickRate}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{stats.clickRate}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Notification Types</CardTitle>
                <CardDescription>
                  Performance by notification type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span>Quiz Available</span>
                    <Badge variant="outline">78% engagement</Badge>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span>Announcements</span>
                    <Badge variant="outline">65% engagement</Badge>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span>Achievements</span>
                    <Badge variant="outline">92% engagement</Badge>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span>System Updates</span>
                    <Badge variant="outline">45% engagement</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
