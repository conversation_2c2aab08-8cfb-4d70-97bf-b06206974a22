import React from 'react'
import { Document, Page, Text, View, StyleSheet, pdf } from '@react-pdf/renderer'

// Enhanced PDF Styles based on PrintQuizFormat
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 20,
    fontFamily: 'Times-Roman',
    fontSize: 12,
    lineHeight: 1.6,
  },
  header: {
    textAlign: 'center',
    borderBottom: '2px solid #000',
    paddingBottom: 20,
    marginBottom: 30,
  },
  institution: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  testTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    margin: '15px 0',
  },
  testInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: '20px 0',
    fontSize: 14,
  },
  studentInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: '20px 0',
    fontSize: 12,
    borderBottom: '1px solid #ccc',
    paddingBottom: 10,
  },
  instructions: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    margin: '20px 0',
    borderLeft: '4px solid #333',
  },
  instructionsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  instructionsList: {
    fontSize: 11,
    lineHeight: 1.4,
  },
  questionsSection: {
    marginTop: 20,
  },
  question: {
    marginBottom: 20,
    pageBreakInside: 'avoid',
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    fontWeight: 'bold',
  },
  questionText: {
    fontSize: 12,
    marginBottom: 8,
  },
  options: {
    marginLeft: 20,
    marginTop: 8,
  },
  option: {
    marginBottom: 4,
    fontSize: 11,
  },
  answerSpace: {
    margin: '20px 0',
    minHeight: 60,
    borderBottom: '1px dotted #666',
  },
  answerKey: {
    pageBreakBefore: 'always',
    marginTop: 30,
  },
  answerKeyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  answerItem: {
    marginBottom: 10,
    fontSize: 11,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    textAlign: 'center',
    fontSize: 8,
    color: '#666',
  },
  // Certificate-specific styles
  certificateContainer: {
    padding: 40,
    border: '8px double #d97706',
    borderRadius: 20,
    backgroundColor: '#fffbeb',
    margin: 20,
    textAlign: 'center',
  },
  certificateHeader: {
    marginBottom: 30,
    borderBottom: '3px solid #d97706',
    paddingBottom: 20,
  },
  certificateTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#d97706',
    marginBottom: 10,
    fontFamily: 'Times-Roman',
  },
  certificateSubtitle: {
    fontSize: 18,
    color: '#92400e',
    fontStyle: 'italic',
  },
  certificateBody: {
    marginTop: 40,
    marginBottom: 40,
    lineHeight: 2,
  },
  certificateText: {
    fontSize: 16,
    color: '#1f2937',
    marginBottom: 15,
  },
  certificateStudentName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#d97706',
    margin: '20px 0',
    textDecoration: 'underline',
  },
  certificateCourseName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    margin: '15px 0',
  },
  certificateFooter: {
    marginTop: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '2px solid #d97706',
    paddingTop: 20,
  },
  certificateSignature: {
    textAlign: 'center',
    width: '40%',
  },
  certificateDate: {
    textAlign: 'center',
    width: '40%',
  },
  certificateId: {
    position: 'absolute',
    bottom: 10,
    right: 20,
    fontSize: 8,
    color: '#6b7280',
  },
  // Result-specific styles
  resultHeader: {
    backgroundColor: '#3b82f6',
    color: 'white',
    padding: 20,
    marginBottom: 20,
    borderRadius: 8,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  resultSubtitle: {
    fontSize: 14,
  },
  scoreSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f9fa',
    padding: 15,
    marginBottom: 20,
    borderRadius: 8,
  },
  scoreItem: {
    textAlign: 'center',
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  scoreLabel: {
    fontSize: 10,
    color: '#666',
    marginTop: 5,
  },
  table: {
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableCol: {
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderColor: '#e5e7eb',
    padding: 8,
    fontSize: 10,
  },
  tableHeader: {
    backgroundColor: '#f3f4f6',
    fontWeight: 'bold',
  },
  correctAnswer: {
    color: '#059669',
    fontWeight: 'bold',
  },
  incorrectAnswer: {
    color: '#dc2626',
    fontWeight: 'bold',
  },
})

// Data interfaces
export interface QuizData {
  id: string
  title: string
  description?: string
  questions: QuestionData[]
  metadata?: {
    totalPoints: number
    estimatedDuration: number
    difficulty?: string
    tags?: string[]
  }
  createdAt?: string
  updatedAt?: string
}

export interface QuestionData {
  id: string
  text: string
  type: 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY'
  options?: string[]
  correctAnswer: string
  explanation?: string
  points: number
  order?: number
}

export interface StudentData {
  id: string
  name: string
  email?: string
  rollNumber?: string
  class?: string
  section?: string
}

export interface QuizAttemptData {
  id: string
  quiz: QuizData
  student: StudentData
  answers: Record<string, string>
  score: number
  percentage: number
  timeSpent: number // in seconds
  startedAt: string
  completedAt: string
  isCompleted: boolean
}

export interface InstitutionData {
  name: string
  address?: string
  logo?: string
  website?: string
  phone?: string
  email?: string
}

export interface TestPaperOptions {
  institutionName?: string
  testDate?: string
  testType?: string
  duration?: number
  totalMarks?: number
  includeAnswerKey?: boolean
  includeInstructions?: boolean
  instructions?: string[]
  template?: 'modern' | 'classic' | 'minimal' | 'corporate' | 'creative' | 'certificate'
  studentInfo?: {
    showName?: boolean
    showRollNumber?: boolean
    showClass?: boolean
    showSection?: boolean
  }
}

export interface AnalyticsData {
  overview: {
    totalAttempts: number
    averageScore: number
    totalTimeSpent: number
    improvementRate: number
  }
  performanceByCategory?: Record<string, { correct: number; total: number; percentage: number }>
  performanceByDifficulty?: Record<string, { correct: number; total: number; percentage: number }>
  weeklyProgress?: Array<{ week: string; score: number; attempts: number }>
  recentAttempts?: Array<{
    quizTitle: string
    score: number
    percentage: number
    completedAt: string
  }>
}

export interface CertificateData {
  studentName: string
  courseName: string
  completionDate: string
  score?: number
  percentage?: number
  certificateId: string
  institutionName?: string
  instructorName?: string
  validUntil?: string
  achievements?: string[]
}

// Enhanced PDF Generator Class
export class EnhancedPDFGenerator {
  private options: TestPaperOptions
  private templateStyles: any

  constructor(options: TestPaperOptions = {}) {
    this.options = {
      includeAnswerKey: true,
      includeInstructions: true,
      template: 'modern',
      instructions: [
        'Read all questions carefully before answering.',
        'Answer all questions in the space provided.',
        'Use blue or black ink only.',
        'Mobile phones and electronic devices are not allowed.',
        'Any form of unfair means will result in disqualification.'
      ],
      studentInfo: {
        showName: true,
        showRollNumber: true,
        showClass: true,
        showSection: true
      },
      ...options
    }

    // Set template-specific styles
    this.templateStyles = this.getTemplateStyles(this.options.template || 'modern')
  }

  private getTemplateStyles(template: string) {
    const baseStyles = styles

    switch (template) {
      case 'classic':
        return {
          ...baseStyles,
          page: { ...baseStyles.page, fontFamily: 'Times-Roman', fontSize: 11 },
          header: { ...baseStyles.header, borderBottom: '3px solid #000' },
          resultHeader: { ...baseStyles.resultHeader, backgroundColor: '#2c3e50' },
          institution: { ...baseStyles.institution, fontSize: 20, fontWeight: 'bold' }
        }

      case 'minimal':
        return {
          ...baseStyles,
          page: { ...baseStyles.page, fontFamily: 'Helvetica', fontSize: 10 },
          header: { ...baseStyles.header, borderBottom: '1px solid #ccc', paddingBottom: 10 },
          resultHeader: { ...baseStyles.resultHeader, backgroundColor: '#6b7280' },
          instructions: { ...baseStyles.instructions, backgroundColor: '#f9fafb', borderLeft: '2px solid #6b7280' }
        }

      case 'corporate':
        return {
          ...baseStyles,
          page: { ...baseStyles.page, fontFamily: 'Helvetica-Bold', fontSize: 11 },
          header: { ...baseStyles.header, borderBottom: '4px solid #059669' },
          resultHeader: { ...baseStyles.resultHeader, backgroundColor: '#059669' },
          scoreSection: { ...baseStyles.scoreSection, backgroundColor: '#ecfdf5' }
        }

      case 'creative':
        return {
          ...baseStyles,
          page: { ...baseStyles.page, fontFamily: 'Helvetica', fontSize: 11 },
          header: { ...baseStyles.header, borderBottom: '3px solid #ec4899' },
          resultHeader: { ...baseStyles.resultHeader, backgroundColor: '#ec4899' },
          scoreSection: { ...baseStyles.scoreSection, backgroundColor: '#fdf2f8' }
        }

      case 'certificate':
        return {
          ...baseStyles,
          page: { ...baseStyles.page, fontFamily: 'Times-Roman', fontSize: 12 },
          header: { ...baseStyles.header, borderBottom: '5px double #d97706', paddingBottom: 25 },
          resultHeader: { ...baseStyles.resultHeader, backgroundColor: '#d97706' },
          institution: { ...baseStyles.institution, fontSize: 22, fontWeight: 'bold' }
        }

      case 'modern':
      default:
        return {
          ...baseStyles,
          resultHeader: { ...baseStyles.resultHeader, backgroundColor: '#3b82f6' }
        }
    }
  }

  // Generate Test Paper PDF (blank quiz for printing)
  async generateTestPaperPDF(quiz: QuizData): Promise<Blob> {
    const document = this.createTestPaperDocument(quiz)
    return await pdf(document).toBlob()
  }

  // Generate Quiz Result PDF (completed attempt)
  async generateQuizResultPDF(attempt: QuizAttemptData): Promise<Blob> {
    const document = this.createQuizResultDocument(attempt)
    return await pdf(document).toBlob()
  }

  // Generate Analytics Report PDF
  async generateAnalyticsReportPDF(data: AnalyticsData, studentName: string): Promise<Blob> {
    const document = this.createAnalyticsDocument(data, studentName)
    return await pdf(document).toBlob()
  }

  // Generate Certificate PDF
  async generateCertificatePDF(data: CertificateData): Promise<Blob> {
    const document = this.createCertificateDocument(data)
    return await pdf(document).toBlob()
  }

  private createTestPaperDocument(quiz: QuizData) {
    return React.createElement(Document, null,
      React.createElement(Page, { size: "A4", style: styles.page },
        // Header
        this.createHeader(quiz.title),
        
        // Test Information
        this.createTestInfo(quiz),
        
        // Student Information Section
        this.createStudentInfoSection(),
        
        // Instructions
        this.options.includeInstructions && this.createInstructions(),
        
        // Questions
        this.createQuestionsSection(quiz.questions),
        
        // Answer Key (if enabled)
        this.options.includeAnswerKey && this.createAnswerKey(quiz.questions),
        
        // Footer
        this.createFooter()
      )
    )
  }

  private createQuizResultDocument(attempt: QuizAttemptData) {
    return React.createElement(Document, null,
      React.createElement(Page, { size: "A4", style: this.templateStyles.page },
        // Result Header
        React.createElement(View, { style: this.templateStyles.resultHeader },
          React.createElement(Text, { style: this.templateStyles.resultTitle }, "Quiz Result Report"),
          React.createElement(Text, { style: this.templateStyles.resultSubtitle },
            `${attempt.quiz.title}`)
        ),

        // Student Information Section
        this.createStudentInfoSection(attempt),

        // Quiz Information Section
        this.createQuizInfoSection(attempt),

        // Score Summary
        this.createScoreSection(attempt),

        // Questions with Answers
        this.createResultQuestionsSection(attempt),

        // Footer
        this.createFooter()
      )
    )
  }

  private createAnalyticsDocument(data: AnalyticsData, studentName: string) {
    return React.createElement(Document, null,
      React.createElement(Page, { size: "A4", style: styles.page },
        // Analytics Header
        React.createElement(View, { style: styles.resultHeader },
          React.createElement(Text, { style: styles.resultTitle }, "Learning Analytics Report"),
          React.createElement(Text, { style: styles.resultSubtitle }, `Performance Analysis for ${studentName}`)
        ),

        // Overview Section
        this.createAnalyticsOverview(data.overview),

        // Performance Details
        data.performanceByCategory && this.createPerformanceByCategory(data.performanceByCategory),

        // Recent Attempts
        data.recentAttempts && this.createRecentAttempts(data.recentAttempts),

        // Footer
        this.createFooter()
      )
    )
  }

  private createCertificateDocument(data: CertificateData) {
    return React.createElement(Document, null,
      React.createElement(Page, { size: "A4", style: { ...this.templateStyles.page, padding: 0 } },
        // Certificate Container
        React.createElement(View, { style: styles.certificateContainer },
          // Certificate Header
          React.createElement(View, { style: styles.certificateHeader },
            data.institutionName && React.createElement(Text, { style: { fontSize: 14, color: '#6b7280', marginBottom: 10 } },
              data.institutionName),
            React.createElement(Text, { style: styles.certificateTitle }, "CERTIFICATE"),
            React.createElement(Text, { style: styles.certificateSubtitle }, "of Achievement")
          ),

          // Certificate Body
          React.createElement(View, { style: styles.certificateBody },
            React.createElement(Text, { style: styles.certificateText }, "This is to certify that"),
            React.createElement(Text, { style: styles.certificateStudentName }, data.studentName),
            React.createElement(Text, { style: styles.certificateText }, "has successfully completed"),
            React.createElement(Text, { style: styles.certificateCourseName }, data.courseName),

            // Score information if provided
            data.score && React.createElement(Text, { style: { ...styles.certificateText, marginTop: 20 } },
              `with a score of ${data.score}${data.percentage ? ` (${data.percentage}%)` : ''}`),

            React.createElement(Text, { style: { ...styles.certificateText, marginTop: 20 } },
              `on ${new Date(data.completionDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}`),

            // Achievements if provided
            data.achievements && data.achievements.length > 0 &&
              React.createElement(View, { style: { marginTop: 30 } },
                React.createElement(Text, { style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 } },
                  "Special Achievements:"),
                ...data.achievements.map((achievement, index) =>
                  React.createElement(Text, { key: index, style: { fontSize: 12, marginBottom: 5 } },
                    `• ${achievement}`)
                )
              )
          ),

          // Certificate Footer
          React.createElement(View, { style: styles.certificateFooter },
            React.createElement(View, { style: styles.certificateSignature },
              React.createElement(Text, { style: { borderTop: '1px solid #000', paddingTop: 5, fontSize: 12 } },
                data.instructorName || 'Authorized Signature'),
              React.createElement(Text, { style: { fontSize: 10, color: '#6b7280', marginTop: 5 } },
                'Instructor/Administrator')
            ),
            React.createElement(View, { style: styles.certificateDate },
              React.createElement(Text, { style: { borderTop: '1px solid #000', paddingTop: 5, fontSize: 12 } },
                new Date().toLocaleDateString()),
              React.createElement(Text, { style: { fontSize: 10, color: '#6b7280', marginTop: 5 } },
                'Date Issued')
            )
          ),

          // Certificate ID
          React.createElement(Text, { style: styles.certificateId },
            `Certificate ID: ${data.certificateId}`),

          // Valid until if provided
          data.validUntil && React.createElement(Text, {
            style: { ...styles.certificateId, bottom: 25 } },
            `Valid until: ${new Date(data.validUntil).toLocaleDateString()}`)
        )
      )
    )
  }

  // Helper methods for creating document sections
  private createHeader(title: string) {
    return React.createElement(View, { style: styles.header },
      this.options.institutionName && React.createElement(Text, { style: styles.institution }, this.options.institutionName),
      React.createElement(Text, { style: styles.testTitle }, title),
      this.options.testType && React.createElement(Text, { style: { fontSize: 14, marginTop: 5 } }, this.options.testType)
    )
  }

  private createTestInfo(quiz: QuizData) {
    const totalMarks = this.options.totalMarks || quiz.metadata?.totalPoints || 0
    const duration = this.options.duration || quiz.metadata?.estimatedDuration || 0
    const testDate = this.options.testDate || new Date().toLocaleDateString()

    return React.createElement(View, { style: styles.testInfo },
      React.createElement(Text, null, `Date: ${testDate}`),
      React.createElement(Text, null, `Duration: ${duration} minutes`),
      React.createElement(Text, null, `Total Marks: ${totalMarks}`)
    )
  }

  private createStudentInfoSection(attempt?: QuizAttemptData) {
    if (attempt) {
      // For quiz results - show actual student data
      return React.createElement(View, { style: styles.studentInfo },
        React.createElement(Text, null, `Student: ${attempt.student.name}`),
        attempt.student.email && React.createElement(Text, null, `Email: ${attempt.student.email}`),
        React.createElement(Text, null, `Completed: ${new Date(attempt.completedAt).toLocaleString()}`),
        React.createElement(Text, null, `Time Taken: ${Math.round(attempt.timeSpent / 60)} minutes`)
      )
    }

    // For test papers - show blank fields
    if (!this.options.studentInfo) return null

    const fields = []
    if (this.options.studentInfo.showName) fields.push('Name: _________________________')
    if (this.options.studentInfo.showRollNumber) fields.push('Roll No: ___________')
    if (this.options.studentInfo.showClass) fields.push('Class: ___________')
    if (this.options.studentInfo.showSection) fields.push('Section: ___________')

    return React.createElement(View, { style: styles.studentInfo },
      ...fields.map((field, index) =>
        React.createElement(Text, { key: index }, field)
      )
    )
  }

  private createQuizInfoSection(attempt: QuizAttemptData) {
    return React.createElement(View, { style: { marginBottom: 20, padding: 15, backgroundColor: '#f8f9fa', borderRadius: 8 } },
      React.createElement(Text, { style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 } }, "Quiz Information"),
      React.createElement(View, { style: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 5 } },
        React.createElement(Text, { style: { fontSize: 11 } }, `Total Questions: ${attempt.quiz.questions.length}`),
        React.createElement(Text, { style: { fontSize: 11 } }, `Total Points: ${attempt.quiz.metadata?.totalPoints || 0}`)
      ),
      React.createElement(View, { style: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 5 } },
        React.createElement(Text, { style: { fontSize: 11 } }, `Difficulty: ${attempt.quiz.metadata?.difficulty || 'Not specified'}`),
        React.createElement(Text, { style: { fontSize: 11 } }, `Duration: ${attempt.quiz.metadata?.estimatedDuration || 0} minutes`)
      ),
      attempt.quiz.metadata?.tags && attempt.quiz.metadata.tags.length > 0 &&
        React.createElement(Text, { style: { fontSize: 11, marginTop: 5 } },
          `Topics: ${attempt.quiz.metadata.tags.join(', ')}`)
    )
  }

  private createInstructions() {
    return React.createElement(View, { style: styles.instructions },
      React.createElement(Text, { style: styles.instructionsTitle }, "Instructions:"),
      React.createElement(View, { style: styles.instructionsList },
        ...(this.options.instructions || []).map((instruction, index) =>
          React.createElement(Text, { key: index, style: { marginBottom: 4 } }, `${index + 1}. ${instruction}`)
        )
      )
    )
  }

  private createQuestionsSection(questions: QuestionData[]) {
    return React.createElement(View, { style: styles.questionsSection },
      ...questions.map((question, index) => this.createQuestion(question, index))
    )
  }

  private createQuestion(question: QuestionData, index: number) {
    return React.createElement(View, { key: question.id, style: styles.question },
      // Question Header
      React.createElement(View, { style: styles.questionHeader },
        React.createElement(Text, null, `Q${index + 1}. ${question.text}`),
        React.createElement(Text, null, `[${question.points} mark${question.points !== 1 ? 's' : ''}]`)
      ),

      // Question Options/Answer Space
      this.createQuestionContent(question)
    )
  }

  private createQuestionContent(question: QuestionData) {
    if (question.type === 'MCQ' && question.options) {
      return React.createElement(View, { style: styles.options },
        ...question.options.map((option, index) =>
          React.createElement(Text, { key: index, style: styles.option },
            `(${String.fromCharCode(65 + index)}) ${option}`)
        )
      )
    } else if (question.type === 'TRUE_FALSE') {
      return React.createElement(View, { style: styles.options },
        React.createElement(Text, { style: styles.option }, "(A) True"),
        React.createElement(Text, { style: styles.option }, "(B) False")
      )
    } else {
      return React.createElement(View, { style: styles.answerSpace },
        React.createElement(Text, { style: { fontStyle: 'italic' } }, "Answer:")
      )
    }
  }

  private createAnswerKey(questions: QuestionData[]) {
    return React.createElement(View, { style: styles.answerKey },
      React.createElement(Text, { style: styles.answerKeyTitle }, "Answer Key"),
      ...questions.map((question, index) =>
        React.createElement(View, { key: question.id, style: styles.answerItem },
          React.createElement(Text, { style: { fontWeight: 'bold' } }, `Q${index + 1}: ${question.correctAnswer}`),
          question.explanation && React.createElement(Text, { style: { fontStyle: 'italic', marginTop: 4 } },
            `Explanation: ${question.explanation}`)
        )
      )
    )
  }

  private createScoreSection(attempt: QuizAttemptData) {
    const timeSpentMinutes = Math.round(attempt.timeSpent / 60)
    const totalQuestions = attempt.quiz.questions.length
    const correctAnswers = Object.entries(attempt.answers).filter(([questionId, answer]) => {
      const question = attempt.quiz.questions.find(q => q.id === questionId)
      return question && question.correctAnswer === answer
    }).length

    return React.createElement(View, { style: styles.scoreSection },
      React.createElement(View, { style: styles.scoreItem },
        React.createElement(Text, { style: styles.scoreValue }, `${attempt.score}`),
        React.createElement(Text, { style: styles.scoreLabel }, "Total Score")
      ),
      React.createElement(View, { style: styles.scoreItem },
        React.createElement(Text, { style: styles.scoreValue }, `${attempt.percentage.toFixed(1)}%`),
        React.createElement(Text, { style: styles.scoreLabel }, "Percentage")
      ),
      React.createElement(View, { style: styles.scoreItem },
        React.createElement(Text, { style: styles.scoreValue }, `${correctAnswers}/${totalQuestions}`),
        React.createElement(Text, { style: styles.scoreLabel }, "Correct Answers")
      ),
      React.createElement(View, { style: styles.scoreItem },
        React.createElement(Text, { style: styles.scoreValue }, `${timeSpentMinutes}m`),
        React.createElement(Text, { style: styles.scoreLabel }, "Time Spent")
      )
    )
  }

  private createResultQuestionsSection(attempt: QuizAttemptData) {
    return React.createElement(View, { style: { marginTop: 20 } },
      React.createElement(Text, { style: { fontSize: 16, fontWeight: 'bold', marginBottom: 15 } }, "Question-wise Analysis"),

      // Table Header
      React.createElement(View, { style: styles.table },
        React.createElement(View, { style: styles.tableRow },
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '10%' }] }, "Q.No"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '40%' }] }, "Question"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '15%' }] }, "Your Answer"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '15%' }] }, "Correct Answer"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '10%' }] }, "Points"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '10%' }] }, "Result")
        ),

        // Question Rows
        ...attempt.quiz.questions.map((question, index) => {
          const userAnswer = attempt.answers[question.id] || 'Not Answered'
          const isCorrect = userAnswer === question.correctAnswer
          const pointsEarned = isCorrect ? question.points : 0

          return React.createElement(View, { key: question.id, style: styles.tableRow },
            React.createElement(Text, { style: [styles.tableCol, { width: '10%' }] }, `${index + 1}`),
            React.createElement(Text, { style: [styles.tableCol, { width: '40%' }] },
              question.text.length > 50 ? question.text.substring(0, 50) + '...' : question.text),
            React.createElement(Text, { style: [styles.tableCol, { width: '15%' }, isCorrect ? styles.correctAnswer : styles.incorrectAnswer] },
              userAnswer),
            React.createElement(Text, { style: [styles.tableCol, { width: '15%' }] }, question.correctAnswer),
            React.createElement(Text, { style: [styles.tableCol, { width: '10%' }] }, `${pointsEarned}/${question.points}`),
            React.createElement(Text, { style: [styles.tableCol, { width: '10%' }, isCorrect ? styles.correctAnswer : styles.incorrectAnswer] },
              isCorrect ? '✓' : '✗')
          )
        })
      )
    )
  }

  private createAnalyticsOverview(overview: AnalyticsData['overview']) {
    return React.createElement(View, { style: { marginTop: 20 } },
      React.createElement(Text, { style: { fontSize: 16, fontWeight: 'bold', marginBottom: 15 } }, "Performance Overview"),

      React.createElement(View, { style: styles.scoreSection },
        React.createElement(View, { style: styles.scoreItem },
          React.createElement(Text, { style: styles.scoreValue }, `${overview.totalAttempts}`),
          React.createElement(Text, { style: styles.scoreLabel }, "Total Attempts")
        ),
        React.createElement(View, { style: styles.scoreItem },
          React.createElement(Text, { style: styles.scoreValue }, `${overview.averageScore.toFixed(1)}%`),
          React.createElement(Text, { style: styles.scoreLabel }, "Average Score")
        ),
        React.createElement(View, { style: styles.scoreItem },
          React.createElement(Text, { style: styles.scoreValue }, `${overview.totalTimeSpent}m`),
          React.createElement(Text, { style: styles.scoreLabel }, "Total Time")
        ),
        React.createElement(View, { style: styles.scoreItem },
          React.createElement(Text, { style: styles.scoreValue }, `${overview.improvementRate.toFixed(1)}%`),
          React.createElement(Text, { style: styles.scoreLabel }, "Improvement")
        )
      )
    )
  }

  private createPerformanceByCategory(performanceByCategory: Record<string, { correct: number; total: number; percentage: number }>) {
    return React.createElement(View, { style: { marginTop: 20 } },
      React.createElement(Text, { style: { fontSize: 16, fontWeight: 'bold', marginBottom: 15 } }, "Performance by Category"),

      React.createElement(View, { style: styles.table },
        React.createElement(View, { style: styles.tableRow },
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '40%' }] }, "Category"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '20%' }] }, "Correct"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '20%' }] }, "Total"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '20%' }] }, "Percentage")
        ),

        ...Object.entries(performanceByCategory).map(([category, performance]) =>
          React.createElement(View, { key: category, style: styles.tableRow },
            React.createElement(Text, { style: [styles.tableCol, { width: '40%' }] }, category),
            React.createElement(Text, { style: [styles.tableCol, { width: '20%' }] }, `${performance.correct}`),
            React.createElement(Text, { style: [styles.tableCol, { width: '20%' }] }, `${performance.total}`),
            React.createElement(Text, { style: [styles.tableCol, { width: '20%' }] }, `${performance.percentage.toFixed(1)}%`)
          )
        )
      )
    )
  }

  private createRecentAttempts(recentAttempts: Array<{ quizTitle: string; score: number; percentage: number; completedAt: string }>) {
    return React.createElement(View, { style: { marginTop: 20 } },
      React.createElement(Text, { style: { fontSize: 16, fontWeight: 'bold', marginBottom: 15 } }, "Recent Quiz Attempts"),

      React.createElement(View, { style: styles.table },
        React.createElement(View, { style: styles.tableRow },
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '40%' }] }, "Quiz Title"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '20%' }] }, "Score"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '20%' }] }, "Percentage"),
          React.createElement(Text, { style: [styles.tableCol, styles.tableHeader, { width: '20%' }] }, "Date")
        ),

        ...recentAttempts.slice(0, 10).map((attempt, index) =>
          React.createElement(View, { key: index, style: styles.tableRow },
            React.createElement(Text, { style: [styles.tableCol, { width: '40%' }] },
              attempt.quizTitle.length > 30 ? attempt.quizTitle.substring(0, 30) + '...' : attempt.quizTitle),
            React.createElement(Text, { style: [styles.tableCol, { width: '20%' }] }, `${attempt.score}`),
            React.createElement(Text, { style: [styles.tableCol, { width: '20%' }] }, `${attempt.percentage.toFixed(1)}%`),
            React.createElement(Text, { style: [styles.tableCol, { width: '20%' }] },
              new Date(attempt.completedAt).toLocaleDateString())
          )
        )
      )
    )
  }

  private createFooter() {
    return React.createElement(Text, { style: styles.footer },
      `Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()} | Quiz Platform`)
  }
}

// Export convenience functions
export async function generateTestPaperPDF(quiz: QuizData, options?: TestPaperOptions): Promise<Blob> {
  const generator = new EnhancedPDFGenerator(options)
  return await generator.generateTestPaperPDF(quiz)
}

export async function generateQuizResultPDF(attempt: QuizAttemptData, options?: TestPaperOptions): Promise<Blob> {
  const generator = new EnhancedPDFGenerator(options)
  return await generator.generateQuizResultPDF(attempt)
}

export async function generateAnalyticsReportPDF(data: AnalyticsData, studentName: string, options?: TestPaperOptions): Promise<Blob> {
  const generator = new EnhancedPDFGenerator(options)
  return await generator.generateAnalyticsReportPDF(data, studentName)
}

export async function generateCertificatePDF(data: CertificateData, options?: TestPaperOptions): Promise<Blob> {
  const generator = new EnhancedPDFGenerator(options)
  return await generator.generateCertificatePDF(data)
}
