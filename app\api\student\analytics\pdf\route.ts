import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { generateAnalyticsReportPDF } from '@/lib/pdf-generator'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Student-specific analytics PDF generation endpoint
    // This provides immediate PDF download for students

    // Fetch user with all quiz attempts
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        quizAttempts: {
          where: { isCompleted: true },
          include: {
            quiz: {
              select: {
                title: true,
                difficulty: true,
                tags: true
              }
            }
          },
          orderBy: { startedAt: 'desc' }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    if (user.quizAttempts.length === 0) {
      return NextResponse.json(
        { error: 'No quiz attempts found' },
        { status: 400 }
      )
    }

    // Calculate real analytics
    const attempts = user.quizAttempts
    const totalAttempts = attempts.length
    const averageScore = attempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / totalAttempts
    const totalTimeSpent = attempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0)
    
    // Calculate improvement rate (compare first half vs second half of attempts)
    const halfPoint = Math.floor(totalAttempts / 2)
    const firstHalfAvg = halfPoint > 0 
      ? attempts.slice(-halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
      : 0
    const secondHalfAvg = halfPoint > 0
      ? attempts.slice(0, halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
      : 0
    const improvementRate = firstHalfAvg > 0 ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100 : 0

    // Group by category (using first tag as category)
    const categoryStats = attempts.reduce((acc, attempt) => {
      const category = attempt.quiz.tags[0] || 'General'
      if (!acc[category]) {
        acc[category] = { attempts: 0, totalScore: 0, scores: [] }
      }
      acc[category].attempts++
      acc[category].totalScore += attempt.percentage
      acc[category].scores.push(attempt.percentage)
      return acc
    }, {} as Record<string, { attempts: number, totalScore: number, scores: number[] }>)

    const performanceByCategory = Object.entries(categoryStats).map(([category, stats]) => ({
      category,
      attempts: stats.attempts,
      averageScore: stats.totalScore / stats.attempts,
      improvement: stats.scores.length > 1 
        ? ((stats.scores[0] - stats.scores[stats.scores.length - 1]) / stats.scores[stats.scores.length - 1]) * 100
        : 0
    }))

    // Group by difficulty
    const difficultyStats = attempts.reduce((acc, attempt) => {
      const difficulty = attempt.quiz.difficulty
      if (!acc[difficulty]) {
        acc[difficulty] = { attempts: 0, totalScore: 0, passed: 0 }
      }
      acc[difficulty].attempts++
      acc[difficulty].totalScore += attempt.percentage
      if (attempt.percentage >= 70) acc[difficulty].passed++
      return acc
    }, {} as Record<string, { attempts: number, totalScore: number, passed: number }>)

    const performanceByDifficulty = Object.entries(difficultyStats).map(([difficulty, stats]) => ({
      difficulty,
      attempts: stats.attempts,
      averageScore: stats.totalScore / stats.attempts,
      successRate: Math.round((stats.passed / stats.attempts) * 100)
    }))

    // Weekly progress (last 4 weeks)
    const fourWeeksAgo = new Date()
    fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28)
    
    const recentAttempts = attempts.filter(attempt => 
      new Date(attempt.startedAt) >= fourWeeksAgo
    )

    const weeklyProgress = Array.from({ length: 4 }, (_, i) => {
      const weekStart = new Date()
      weekStart.setDate(weekStart.getDate() - (i * 7))
      const weekEnd = new Date()
      weekEnd.setDate(weekEnd.getDate() - ((i - 1) * 7))
      
      const weekAttempts = recentAttempts.filter(attempt => {
        const attemptDate = new Date(attempt.startedAt)
        return attemptDate >= weekStart && attemptDate < weekEnd
      })

      return {
        week: `Week ${4 - i}`,
        attempts: weekAttempts.length,
        averageScore: weekAttempts.length > 0 
          ? weekAttempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / weekAttempts.length
          : 0,
        timeSpent: Math.round(weekAttempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0) / 60)
      }
    }).reverse()

    const analyticsData = {
      overview: {
        totalAttempts,
        averageScore,
        totalTimeSpent: Math.round(totalTimeSpent / 60), // Convert to minutes
        improvementRate
      },
      performanceByCategory,
      performanceByDifficulty,
      weeklyProgress
    }

    // Generate PDF
    const pdfBlob = await generateAnalyticsReportPDF(analyticsData, user.name || 'Unknown User')
    
    // Convert blob to buffer
    const arrayBuffer = await pdfBlob.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Log the PDF generation
    await prisma.pdfExport.create({
      data: {
        type: 'analytics',
        filename: `analytics-${user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`,
        size: buffer.length,
        userId: session.user.id,
        status: 'completed',
        options: JSON.stringify({ userId: session.user.id })
      }
    })

    // Return PDF as response
    const filename = `analytics-report-${user.name?.replace(/\s+/g, '-') || 'user'}-${new Date().toISOString().split('T')[0]}.pdf`
    
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString()
      }
    })

  } catch (error) {
    console.error('Analytics PDF generation error:', error)
    
    // Log the error
    const session = await auth()
    if (session?.user?.id) {
      await prisma.pdfExport.create({
        data: {
          type: 'analytics',
          filename: 'analytics-error.pdf',
          size: 0,
          userId: session.user.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    }

    return NextResponse.json(
      { error: 'Analytics PDF generation failed' },
      { status: 500 }
    )
  }
}
