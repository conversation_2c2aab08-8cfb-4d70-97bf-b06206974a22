import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { generateQuizResultPDF } from '@/lib/enhanced-pdf-generator'
import type { QuizAttemptData } from '@/lib/enhanced-pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ attemptId: string }> }
) {
  // Await params
  const resolvedParams = await params
  const attemptId = resolvedParams.attemptId

  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Fetch the quiz attempt with all related data
    const attempt = await prisma.quizAttempt.findUnique({
      where: {
        id: attemptId,
        userId: session.user.id // Ensure user can only access their own attempts
      },
      include: {
        quiz: {
          include: {
            questions: {
              orderBy: { order: 'asc' }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!attempt) {
      return NextResponse.json(
        { error: 'Quiz attempt not found' },
        { status: 404 }
      )
    }

    if (!attempt.isCompleted) {
      return NextResponse.json(
        { error: 'Quiz attempt is not completed yet' },
        { status: 400 }
      )
    }

    // Calculate rank (optional - can be expensive for large datasets)
    const betterAttempts = await prisma.quizAttempt.count({
      where: {
        quizId: attempt.quizId,
        percentage: { gt: attempt.percentage },
        isCompleted: true
      }
    })
    const rank = betterAttempts + 1

    // Get total attempts for this quiz
    const totalAttempts = await prisma.quizAttempt.count({
      where: {
        quizId: attempt.quizId,
        isCompleted: true
      }
    })

    // Transform database data to enhanced PDF format
    const quizAttemptData: QuizAttemptData = {
      id: attempt.id,
      quiz: {
        id: attempt.quiz.id,
        title: attempt.quiz.title,
        description: attempt.quiz.description || '',
        questions: attempt.quiz.questions.map(question => ({
          id: question.id,
          text: question.text,
          type: question.type as 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY',
          options: question.options || undefined,
          correctAnswer: question.correctAnswer,
          explanation: question.explanation || undefined,
          points: question.points,
          order: question.order
        })),
        metadata: {
          totalPoints: attempt.totalPoints,
          estimatedDuration: attempt.quiz.timeLimit || 0,
          difficulty: attempt.quiz.difficulty || undefined,
          tags: attempt.quiz.tags || undefined
        },
        createdAt: attempt.quiz.createdAt.toISOString(),
        updatedAt: attempt.quiz.updatedAt.toISOString()
      },
      student: {
        id: attempt.user.id,
        name: attempt.user.name || 'Unknown',
        email: attempt.user.email || undefined
      },
      answers: attempt.answers as Record<string, string>,
      score: attempt.score,
      percentage: attempt.percentage,
      timeSpent: attempt.timeSpent || 0, // Keep in seconds for the enhanced generator
      startedAt: attempt.startedAt.toISOString(),
      completedAt: attempt.completedAt?.toISOString() || new Date().toISOString(),
      isCompleted: attempt.isCompleted
    }

    // Generate PDF using enhanced generator
    const pdfBlob = await generateQuizResultPDF(quizAttemptData)
    
    // Convert blob to buffer
    const arrayBuffer = await pdfBlob.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Log the PDF generation
    await prisma.pdfExport.create({
      data: {
        type: 'quiz-result',
        filename: `quiz-result-${attempt.user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`,
        size: buffer.length,
        userId: session.user.id,
        status: 'completed',
        options: JSON.stringify({ attemptId })
      }
    })

    // Return PDF as response
    const filename = `quiz-result-${attempt.quiz.title.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`
    
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString()
      }
    })

  } catch (error) {
    console.error('PDF generation error:', error)
    
    // Log the error
    const session = await auth()
    if (session?.user?.id) {
      await prisma.pdfExport.create({
        data: {
          type: 'quiz-result',
          filename: 'error.pdf',
          size: 0,
          userId: session.user.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          options: JSON.stringify({ attemptId })
        }
      })
    }

    return NextResponse.json(
      { error: 'PDF generation failed' },
      { status: 500 }
    )
  }
}
