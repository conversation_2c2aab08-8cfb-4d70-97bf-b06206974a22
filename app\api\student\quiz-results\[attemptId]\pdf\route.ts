import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { generateQuizResultPDF } from '@/lib/pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ attemptId: string }> }
) {
  // Await params
  const resolvedParams = await params
  const attemptId = resolvedParams.attemptId

  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Fetch the quiz attempt with all related data
    const attempt = await prisma.quizAttempt.findUnique({
      where: {
        id: attemptId,
        userId: session.user.id // Ensure user can only access their own attempts
      },
      include: {
        quiz: {
          include: {
            questions: {
              orderBy: { order: 'asc' }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!attempt) {
      return NextResponse.json(
        { error: 'Quiz attempt not found' },
        { status: 404 }
      )
    }

    if (!attempt.isCompleted) {
      return NextResponse.json(
        { error: 'Quiz attempt is not completed yet' },
        { status: 400 }
      )
    }

    // Calculate rank (optional - can be expensive for large datasets)
    const betterAttempts = await prisma.quizAttempt.count({
      where: {
        quizId: attempt.quizId,
        percentage: { gt: attempt.percentage },
        isCompleted: true
      }
    })
    const rank = betterAttempts + 1

    // Get total attempts for this quiz
    const totalAttempts = await prisma.quizAttempt.count({
      where: {
        quizId: attempt.quizId,
        isCompleted: true
      }
    })

    // Transform database data to PDF format
    const quizResult = {
      id: attempt.id,
      quiz: {
        title: attempt.quiz.title,
        description: attempt.quiz.description || '',
        category: (attempt.quiz.tags && attempt.quiz.tags.length > 0) ? attempt.quiz.tags[0] : 'General',
        difficulty: attempt.quiz.difficulty,
        totalQuestions: attempt.quiz.questions.length,
        totalPoints: attempt.totalPoints,
        duration: attempt.quiz.timeLimit || 0
      },
      student: {
        name: attempt.user.name || 'Unknown',
        email: attempt.user.email || '',
        id: attempt.user.id
      },
      score: attempt.score,
      percentage: attempt.percentage,
      timeSpent: Math.round((attempt.timeSpent || 0) / 60), // Convert to minutes
      completedAt: attempt.completedAt?.toISOString() || new Date().toISOString(),
      questions: attempt.quiz.questions.map(question => {
        const userAnswer = (attempt.answers as any)[question.id]
        const isCorrect = userAnswer === question.correctAnswer

        return {
          id: question.id,
          text: question.text,
          type: question.type.toString(), // Convert enum to string
          options: question.options || undefined, // Handle optional field
          correctAnswer: question.correctAnswer,
          studentAnswer: userAnswer,
          isCorrect,
          points: question.points,
          pointsEarned: isCorrect ? question.points : 0,
          explanation: question.explanation || undefined // Convert null to undefined
        }
      }),
      rank,
      totalAttempts
    }

    // Generate PDF
    const pdfBlob = await generateQuizResultPDF(quizResult)
    
    // Convert blob to buffer
    const arrayBuffer = await pdfBlob.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Log the PDF generation
    await prisma.pdfExport.create({
      data: {
        type: 'quiz-result',
        filename: `quiz-result-${attempt.user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`,
        size: buffer.length,
        userId: session.user.id,
        status: 'completed',
        options: JSON.stringify({ attemptId })
      }
    })

    // Return PDF as response
    const filename = `quiz-result-${attempt.quiz.title.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`
    
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString()
      }
    })

  } catch (error) {
    console.error('PDF generation error:', error)
    
    // Log the error
    const session = await auth()
    if (session?.user?.id) {
      await prisma.pdfExport.create({
        data: {
          type: 'quiz-result',
          filename: 'error.pdf',
          size: 0,
          userId: session.user.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          options: JSON.stringify({ attemptId })
        }
      })
    }

    return NextResponse.json(
      { error: 'PDF generation failed' },
      { status: 500 }
    )
  }
}
