import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

// Server-side PDF generation using Puppeteer (for production)
// For now, we'll use a mock implementation

interface PDFGenerationRequest {
  type: 'quiz-result' | 'analytics' | 'certificate' | 'bulk'
  data: any
  options?: {
    template?: string
    format?: 'A4' | 'Letter'
    orientation?: 'portrait' | 'landscape'
    includeCharts?: boolean
    includeDetails?: boolean
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: PDFGenerationRequest = await request.json()
    const { type, data, options = {} } = body

    // Validate request
    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    // Generate PDF based on type
    let pdfBuffer: Buffer
    let filename: string

    switch (type) {
      case 'quiz-result':
        const result = await generateQuizResultPDF(data, options)
        pdfBuffer = result.buffer
        filename = result.filename
        break

      case 'analytics':
        const analytics = await generateAnalyticsPDF(data, options)
        pdfBuffer = analytics.buffer
        filename = analytics.filename
        break

      case 'certificate':
        const certificate = await generateCertificatePDF(data, options)
        pdfBuffer = certificate.buffer
        filename = certificate.filename
        break

      case 'bulk':
        const bulk = await generateBulkPDF(data, options)
        pdfBuffer = bulk.buffer
        filename = bulk.filename
        break

      default:
        return NextResponse.json(
          { error: 'Invalid PDF type' },
          { status: 400 }
        )
    }

    // Log the generation
    await prisma.pdfExport.create({
      data: {
        type,
        filename,
        size: pdfBuffer.length,
        userId: session.user.id,
        status: 'completed',
        options: JSON.stringify(options)
      }
    })

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    })

  } catch (error) {
    console.error('PDF generation error:', error)
    
    // Log the error
    const session = await auth()
    if (session?.user?.id) {
      await prisma.pdfExport.create({
        data: {
          type: 'unknown',
          filename: 'error.pdf',
          size: 0,
          userId: session.user.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    }

    return NextResponse.json(
      { error: 'PDF generation failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const status = searchParams.get('status')

    // Build query filters
    const where: any = {
      userId: session.user.id
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    // Get PDF exports with pagination
    const [exports, total] = await Promise.all([
      prisma.pdfExport.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.pdfExport.count({ where })
    ])

    return NextResponse.json({
      exports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get PDF exports error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch PDF exports' },
      { status: 500 }
    )
  }
}

// Real PDF generation functions with database integration
async function generateQuizResultPDF(data: { attemptId: string }, options: any): Promise<{ buffer: Buffer, filename: string }> {
  // Fetch real quiz attempt data from database
  const attempt = await prisma.quizAttempt.findUnique({
    where: { id: data.attemptId },
    include: {
      quiz: {
        include: {
          questions: {
            orderBy: { order: 'asc' }
          }
        }
      },
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  })

  if (!attempt) {
    throw new Error('Quiz attempt not found')
  }

  // Transform database data to PDF format
  const quizResult = {
    id: attempt.id,
    quiz: {
      title: attempt.quiz.title,
      description: attempt.quiz.description || '',
      category: attempt.quiz.tags[0] || 'General',
      difficulty: attempt.quiz.difficulty,
      totalQuestions: attempt.quiz.questions.length,
      totalPoints: attempt.totalPoints,
      duration: attempt.quiz.timeLimit || 0
    },
    student: {
      name: attempt.user.name || 'Unknown',
      email: attempt.user.email || '',
      id: attempt.user.id
    },
    score: attempt.score,
    percentage: attempt.percentage,
    timeSpent: Math.round((attempt.timeSpent || 0) / 60), // Convert to minutes
    completedAt: attempt.completedAt?.toISOString() || new Date().toISOString(),
    questions: attempt.quiz.questions.map(question => {
      const userAnswer = (attempt.answers as any)[question.id]
      const isCorrect = userAnswer === question.correctAnswer

      return {
        id: question.id,
        text: question.text,
        type: question.type,
        options: question.options,
        correctAnswer: question.correctAnswer,
        studentAnswer: userAnswer,
        isCorrect,
        points: question.points,
        pointsEarned: isCorrect ? question.points : 0,
        explanation: question.explanation
      }
    }),
    rank: 1, // TODO: Calculate actual rank
    totalAttempts: 1 // TODO: Calculate total attempts
  }

  // Use the existing PDF generator
  const { generateQuizResultPDF: generatePDF } = await import('@/lib/pdf-generator')
  const pdfBlob = await generatePDF(quizResult)

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `quiz-result-${attempt.user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateAnalyticsPDF(data: { userId: string }, options: any): Promise<{ buffer: Buffer, filename: string }> {
  // Fetch real user analytics from database
  const user = await prisma.user.findUnique({
    where: { id: data.userId },
    include: {
      quizAttempts: {
        include: {
          quiz: {
            select: {
              title: true,
              difficulty: true,
              tags: true
            }
          }
        },
        orderBy: { startedAt: 'desc' }
      }
    }
  })

  if (!user) {
    throw new Error('User not found')
  }

  // Calculate real analytics
  const attempts = user.quizAttempts
  const totalAttempts = attempts.length
  const averageScore = totalAttempts > 0
    ? attempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / totalAttempts
    : 0
  const totalTimeSpent = attempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0)

  // Calculate improvement rate (compare first half vs second half of attempts)
  const halfPoint = Math.floor(totalAttempts / 2)
  const firstHalfAvg = halfPoint > 0
    ? attempts.slice(-halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
    : 0
  const secondHalfAvg = halfPoint > 0
    ? attempts.slice(0, halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
    : 0
  const improvementRate = firstHalfAvg > 0 ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100 : 0

  // Group by category (using first tag as category)
  const categoryStats = attempts.reduce((acc, attempt) => {
    const category = attempt.quiz.tags[0] || 'General'
    if (!acc[category]) {
      acc[category] = { attempts: 0, totalScore: 0, scores: [] }
    }
    acc[category].attempts++
    acc[category].totalScore += attempt.percentage
    acc[category].scores.push(attempt.percentage)
    return acc
  }, {} as Record<string, { attempts: number, totalScore: number, scores: number[] }>)

  const performanceByCategory = Object.entries(categoryStats).map(([category, stats]) => ({
    category,
    attempts: stats.attempts,
    averageScore: stats.totalScore / stats.attempts,
    improvement: stats.scores.length > 1
      ? ((stats.scores[0] - stats.scores[stats.scores.length - 1]) / stats.scores[stats.scores.length - 1]) * 100
      : 0
  }))

  // Group by difficulty
  const difficultyStats = attempts.reduce((acc, attempt) => {
    const difficulty = attempt.quiz.difficulty
    if (!acc[difficulty]) {
      acc[difficulty] = { attempts: 0, totalScore: 0, passed: 0 }
    }
    acc[difficulty].attempts++
    acc[difficulty].totalScore += attempt.percentage
    if (attempt.percentage >= 70) acc[difficulty].passed++
    return acc
  }, {} as Record<string, { attempts: number, totalScore: number, passed: number }>)

  const performanceByDifficulty = Object.entries(difficultyStats).map(([difficulty, stats]) => ({
    difficulty,
    attempts: stats.attempts,
    averageScore: stats.totalScore / stats.attempts,
    successRate: Math.round((stats.passed / stats.attempts) * 100)
  }))

  // Weekly progress (last 4 weeks)
  const fourWeeksAgo = new Date()
  fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28)

  const recentAttempts = attempts.filter(attempt =>
    new Date(attempt.startedAt) >= fourWeeksAgo
  )

  const weeklyProgress = Array.from({ length: 4 }, (_, i) => {
    const weekStart = new Date()
    weekStart.setDate(weekStart.getDate() - (i * 7))
    const weekEnd = new Date()
    weekEnd.setDate(weekEnd.getDate() - ((i - 1) * 7))

    const weekAttempts = recentAttempts.filter(attempt => {
      const attemptDate = new Date(attempt.startedAt)
      return attemptDate >= weekStart && attemptDate < weekEnd
    })

    return {
      week: `Week ${4 - i}`,
      attempts: weekAttempts.length,
      averageScore: weekAttempts.length > 0
        ? weekAttempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / weekAttempts.length
        : 0,
      timeSpent: Math.round(weekAttempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0) / 60)
    }
  }).reverse()

  const analyticsData = {
    overview: {
      totalAttempts,
      averageScore,
      totalTimeSpent: Math.round(totalTimeSpent / 60), // Convert to minutes
      improvementRate
    },
    performanceByCategory,
    performanceByDifficulty,
    weeklyProgress
  }

  // Use the existing PDF generator
  const { generateAnalyticsReportPDF } = await import('@/lib/pdf-generator')
  const pdfBlob = await generateAnalyticsReportPDF(analyticsData, user.name || 'Unknown User')

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `analytics-${user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateCertificatePDF(data: any, options: any): Promise<{ buffer: Buffer, filename: string }> {
  // Use the existing PDF generator for certificates
  const { generateCertificatePDF: generateCertPDF } = await import('@/lib/pdf-generator')

  // Ensure we have the required certificate data
  const certificateData = {
    studentName: data.studentName || 'Unknown Student',
    quizTitle: data.quizTitle || 'Quiz Completion',
    score: data.score || 0,
    completedAt: data.completedAt || new Date().toISOString(),
    certificateId: data.certificateId || `cert-${Date.now()}`
  }

  const pdfBlob = await generateCertPDF(certificateData)

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `certificate-${certificateData.studentName.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateBulkPDF(data: any, options: any): Promise<{ buffer: Buffer, filename: string }> {
  // Bulk PDF generation - combine multiple reports into one PDF
  const { PDFGenerator } = await import('@/lib/pdf-generator')

  const generator = new PDFGenerator({
    title: 'Bulk Export Report',
    author: 'Quiz Platform',
    subject: 'Bulk Data Export',
    creator: 'Quiz Platform'
  })

  // Initialize the PDF document
  generator.doc.setFontSize(20)
  generator.doc.text('Bulk Export Report', 20, 30)
  generator.doc.setFontSize(12)
  generator.doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45)

  let yPosition = 60

  // Add summary information
  generator.doc.setFontSize(14)
  generator.doc.text('Export Summary', 20, yPosition)
  yPosition += 15

  generator.doc.setFontSize(10)
  generator.doc.text(`Export Type: ${data.type || 'Mixed'}`, 20, yPosition)
  yPosition += 10
  generator.doc.text(`Total Items: ${data.items?.length || 0}`, 20, yPosition)
  yPosition += 10
  generator.doc.text(`Date Range: ${data.dateRange?.start || 'N/A'} to ${data.dateRange?.end || 'N/A'}`, 20, yPosition)
  yPosition += 20

  // Add items if provided
  if (data.items && Array.isArray(data.items)) {
    generator.doc.setFontSize(14)
    generator.doc.text('Export Items', 20, yPosition)
    yPosition += 15

    data.items.forEach((item: any, index: number) => {
      if (yPosition > 250) { // Check if we need a new page
        generator.doc.addPage()
        yPosition = 20
      }

      generator.doc.setFontSize(10)
      generator.doc.text(`${index + 1}. ${item.title || item.name || `Item ${index + 1}`}`, 20, yPosition)
      yPosition += 8

      if (item.description) {
        generator.doc.text(`   ${item.description.substring(0, 100)}${item.description.length > 100 ? '...' : ''}`, 20, yPosition)
        yPosition += 8
      }

      yPosition += 5
    })
  }

  // Generate the PDF blob
  const pdfBlob = new Blob([generator.doc.output('blob')], { type: 'application/pdf' })

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `bulk-export-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const exportId = searchParams.get('id')

    if (!exportId) {
      return NextResponse.json(
        { error: 'Export ID is required' },
        { status: 400 }
      )
    }

    // Check if export exists and belongs to user
    const pdfExport = await prisma.pdfExport.findFirst({
      where: {
        id: exportId,
        userId: session.user.id
      }
    })

    if (!pdfExport) {
      return NextResponse.json(
        { error: 'PDF export not found' },
        { status: 404 }
      )
    }

    // Delete the export record
    await prisma.pdfExport.delete({
      where: { id: exportId }
    })

    return NextResponse.json({
      success: true,
      message: 'PDF export deleted successfully'
    })

  } catch (error) {
    console.error('Delete PDF export error:', error)
    return NextResponse.json(
      { error: 'Failed to delete PDF export' },
      { status: 500 }
    )
  }
}
