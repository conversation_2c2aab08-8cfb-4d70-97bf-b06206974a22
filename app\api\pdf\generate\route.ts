import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'
import { generateQuizResultPDF, generateAnalyticsReportPDF, generateTestPaperPDF } from '@/lib/enhanced-pdf-generator'
import type { QuizAttemptData, AnalyticsData, QuizData, TestPaperOptions } from '@/lib/enhanced-pdf-generator'

interface PDFGenerationRequest {
  type: 'quiz-result' | 'analytics' | 'test-paper' | 'bulk'
  data: any
  options?: TestPaperOptions & {
    template?: string
    format?: 'A4' | 'Letter'
    orientation?: 'portrait' | 'landscape'
    includeCharts?: boolean
    includeDetails?: boolean
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: PDFGenerationRequest = await request.json()
    const { type, data, options = {} } = body

    // Validate request
    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    // Generate PDF based on type
    let pdfBuffer: Buffer
    let filename: string

    switch (type) {
      case 'quiz-result':
        const result = await generateQuizResultPDFWithData(data, options)
        pdfBuffer = result.buffer
        filename = result.filename
        break

      case 'analytics':
        const analytics = await generateAnalyticsPDFWithData(data, options)
        pdfBuffer = analytics.buffer
        filename = analytics.filename
        break

      case 'test-paper':
        const testPaper = await generateTestPaperPDFWithData(data, options)
        pdfBuffer = testPaper.buffer
        filename = testPaper.filename
        break

      case 'bulk':
        const bulk = await generateBulkPDFWithData(data, options)
        pdfBuffer = bulk.buffer
        filename = bulk.filename
        break

      default:
        return NextResponse.json(
          { error: 'Invalid PDF type. Supported types: quiz-result, analytics, test-paper, bulk' },
          { status: 400 }
        )
    }

    // Log the generation
    await prisma.pdfExport.create({
      data: {
        type,
        filename,
        size: pdfBuffer.length,
        userId: session.user.id,
        status: 'completed',
        options: JSON.stringify(options)
      }
    })

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    })

  } catch (error) {
    console.error('PDF generation error:', error)
    
    // Log the error
    const session = await auth()
    if (session?.user?.id) {
      await prisma.pdfExport.create({
        data: {
          type: 'unknown',
          filename: 'error.pdf',
          size: 0,
          userId: session.user.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    }

    return NextResponse.json(
      { error: 'PDF generation failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const status = searchParams.get('status')

    // Build query filters
    const where: any = {
      userId: session.user.id
    }

    if (type) {
      where.type = type
    }

    if (status) {
      where.status = status
    }

    // Get PDF exports with pagination
    const [exports, total] = await Promise.all([
      prisma.pdfExport.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.pdfExport.count({ where })
    ])

    return NextResponse.json({
      exports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get PDF exports error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch PDF exports' },
      { status: 500 }
    )
  }
}

// Enhanced PDF generation functions with database integration
async function generateQuizResultPDFWithData(data: { attemptId: string }, options: TestPaperOptions): Promise<{ buffer: Buffer, filename: string }> {
  // Fetch real quiz attempt data from database
  const attempt = await prisma.quizAttempt.findUnique({
    where: { id: data.attemptId },
    include: {
      quiz: {
        include: {
          questions: {
            orderBy: { order: 'asc' }
          }
        }
      },
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  })

  if (!attempt) {
    throw new Error('Quiz attempt not found')
  }

  // Transform database data to enhanced PDF format
  const quizAttemptData: QuizAttemptData = {
    id: attempt.id,
    quiz: {
      id: attempt.quiz.id,
      title: attempt.quiz.title,
      description: attempt.quiz.description || '',
      questions: attempt.quiz.questions.map(question => ({
        id: question.id,
        text: question.text,
        type: question.type as 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY',
        options: question.options || undefined,
        correctAnswer: question.correctAnswer,
        explanation: question.explanation || undefined,
        points: question.points,
        order: question.order
      })),
      metadata: {
        totalPoints: attempt.totalPoints,
        estimatedDuration: attempt.quiz.timeLimit || 0,
        difficulty: attempt.quiz.difficulty || undefined,
        tags: attempt.quiz.tags || undefined
      },
      createdAt: attempt.quiz.createdAt.toISOString(),
      updatedAt: attempt.quiz.updatedAt.toISOString()
    },
    student: {
      id: attempt.user.id,
      name: attempt.user.name || 'Unknown',
      email: attempt.user.email || undefined
    },
    answers: attempt.answers as Record<string, string>,
    score: attempt.score,
    percentage: attempt.percentage,
    timeSpent: attempt.timeSpent || 0,
    startedAt: attempt.startedAt.toISOString(),
    completedAt: attempt.completedAt?.toISOString() || new Date().toISOString(),
    isCompleted: attempt.isCompleted
  }

  // Use the enhanced PDF generator
  const pdfBlob = await generateQuizResultPDF(quizAttemptData, options)

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `quiz-result-${attempt.user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateAnalyticsPDFWithData(data: { userId: string }, options: TestPaperOptions): Promise<{ buffer: Buffer, filename: string }> {
  // Fetch real user analytics from database
  const user = await prisma.user.findUnique({
    where: { id: data.userId },
    include: {
      quizAttempts: {
        where: { isCompleted: true },
        include: {
          quiz: {
            select: {
              title: true,
              difficulty: true,
              tags: true
            }
          }
        },
        orderBy: { startedAt: 'desc' }
      }
    }
  })

  if (!user) {
    throw new Error('User not found')
  }

  if (user.quizAttempts.length === 0) {
    throw new Error('No quiz attempts found for analytics')
  }

  // Calculate real analytics (similar to student analytics endpoint)
  const attempts = user.quizAttempts
  const totalAttempts = attempts.length
  const averageScore = attempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / totalAttempts
  const totalTimeSpent = attempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0)

  const halfPoint = Math.floor(totalAttempts / 2)
  const firstHalfAvg = halfPoint > 0
    ? attempts.slice(-halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
    : 0
  const secondHalfAvg = halfPoint > 0
    ? attempts.slice(0, halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
    : 0
  const improvementRate = firstHalfAvg > 0 ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100 : 0

  const recentAttemptsData = attempts.slice(0, 10).map(attempt => ({
    quizTitle: attempt.quiz.title,
    score: attempt.score,
    percentage: attempt.percentage,
    completedAt: attempt.completedAt?.toISOString() || attempt.startedAt.toISOString()
  }))

  const analyticsData: AnalyticsData = {
    overview: {
      totalAttempts,
      averageScore,
      totalTimeSpent: Math.round(totalTimeSpent / 60),
      improvementRate
    },
    recentAttempts: recentAttemptsData
  }

  // Use the enhanced PDF generator
  const pdfBlob = await generateAnalyticsReportPDF(analyticsData, user.name || 'Unknown User', options)

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `analytics-${user.name?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateTestPaperPDFWithData(data: { quizId: string }, options: TestPaperOptions): Promise<{ buffer: Buffer, filename: string }> {
  // Fetch quiz data for test paper generation
  const quiz = await prisma.quiz.findUnique({
    where: { id: data.quizId },
    include: {
      questions: {
        orderBy: { order: 'asc' }
      }
    }
  })

  if (!quiz) {
    throw new Error('Quiz not found')
  }

  // Transform database data to enhanced PDF format
  const quizData: QuizData = {
    id: quiz.id,
    title: quiz.title,
    description: quiz.description || '',
    questions: quiz.questions.map(question => ({
      id: question.id,
      text: question.text,
      type: question.type as 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY',
      options: question.options || undefined,
      correctAnswer: question.correctAnswer,
      explanation: question.explanation || undefined,
      points: question.points,
      order: question.order
    })),
    metadata: {
      totalPoints: quiz.questions.reduce((sum, q) => sum + q.points, 0),
      estimatedDuration: quiz.timeLimit || 0,
      difficulty: quiz.difficulty || undefined,
      tags: quiz.tags || undefined
    },
    createdAt: quiz.createdAt.toISOString(),
    updatedAt: quiz.updatedAt.toISOString()
  }

  // Use the enhanced PDF generator
  const pdfBlob = await generateTestPaperPDF(quizData, options)

  // Convert blob to buffer
  const arrayBuffer = await pdfBlob.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const filename = `test-paper-${quiz.title.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`

  return { buffer, filename }
}

async function generateBulkPDFWithData(data: any, options: TestPaperOptions): Promise<{ buffer: Buffer, filename: string }> {
  // For now, return an error as bulk PDF generation needs more complex implementation
  throw new Error('Bulk PDF generation not yet implemented with enhanced generator')
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const exportId = searchParams.get('id')

    if (!exportId) {
      return NextResponse.json(
        { error: 'Export ID is required' },
        { status: 400 }
      )
    }

    // Check if export exists and belongs to user
    const pdfExport = await prisma.pdfExport.findFirst({
      where: {
        id: exportId,
        userId: session.user.id
      }
    })

    if (!pdfExport) {
      return NextResponse.json(
        { error: 'PDF export not found' },
        { status: 404 }
      )
    }

    // Delete the export record
    await prisma.pdfExport.delete({
      where: { id: exportId }
    })

    return NextResponse.json({
      success: true,
      message: 'PDF export deleted successfully'
    })

  } catch (error) {
    console.error('Delete PDF export error:', error)
    return NextResponse.json(
      { error: 'Failed to delete PDF export' },
      { status: 500 }
    )
  }
}
