import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateQuizResultPDF, generateAnalyticsReportPDF, generateCertificatePDF } from '@/lib/pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const exportId = params.id

    // Find the export record
    const exportRecord = await prisma.pdfExport.findUnique({
      where: { id: exportId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!exportRecord) {
      return new NextResponse('Export not found', { status: 404 })
    }

    if (exportRecord.status !== 'completed') {
      return new NextResponse('Export is not ready for download', { status: 400 })
    }

    // Generate the PDF based on the export type
    let pdfBlob: Blob

    try {
      switch (exportRecord.type) {
        case 'quiz-result':
          // In a real implementation, you would fetch the actual quiz result data
          // For now, we'll generate a basic PDF with available information
          const quizResultData = {
            id: exportRecord.id,
            quiz: {
              title: `Quiz Result Export`,
              description: 'Generated quiz result report',
              category: 'Export',
              difficulty: 'MEDIUM' as const,
              totalQuestions: 10,
              totalPoints: 100,
              duration: 30
            },
            student: {
              name: exportRecord.user?.name || 'Unknown Student',
              email: exportRecord.user?.email || '<EMAIL>',
              id: exportRecord.userId
            },
            score: 85,
            percentage: 85,
            timeSpent: 1800,
            completedAt: exportRecord.createdAt.toISOString(),
            questions: [],
            rank: 1,
            totalAttempts: 1
          }
          pdfBlob = await generateQuizResultPDF(quizResultData)
          break

        case 'analytics':
          // Generate analytics PDF with basic data
          const analyticsData = {
            overview: {
              totalAttempts: 10,
              averageScore: 85,
              totalTimeSpent: 3600,
              improvementRate: 15
            },
            performanceByCategory: [],
            performanceByDifficulty: [],
            weeklyProgress: []
          }
          pdfBlob = await generateAnalyticsReportPDF(analyticsData, exportRecord.user?.name || 'Student Analytics')
          break

        case 'certificate':
          // Generate certificate PDF
          const certificateData = {
            studentName: exportRecord.user?.name || 'Student',
            quizTitle: 'Course Completion Certificate',
            score: 85,
            completedAt: exportRecord.createdAt.toISOString(),
            certificateId: `CERT-${exportRecord.id.slice(0, 8).toUpperCase()}`
          }
          pdfBlob = await generateCertificatePDF(certificateData)
          break

        default:
          return new NextResponse('Unsupported export type', { status: 400 })
      }

      // Convert blob to buffer
      const buffer = Buffer.from(await pdfBlob.arrayBuffer())

      // Return the PDF file
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${exportRecord.filename}"`,
          'Content-Length': buffer.length.toString()
        }
      })

    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError)
      return new NextResponse('Failed to generate PDF', { status: 500 })
    }

  } catch (error) {
    console.error('Error serving PDF file:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}
