import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateQuizResultPDF, generateAnalyticsReportPDF } from '@/lib/enhanced-pdf-generator'
import type { QuizAttemptData, AnalyticsData } from '@/lib/enhanced-pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const exportId = params.id

    // Find the export record
    const exportRecord = await prisma.pdfExport.findUnique({
      where: { id: exportId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!exportRecord) {
      return new NextResponse('Export not found', { status: 404 })
    }

    if (exportRecord.status !== 'completed') {
      return new NextResponse('Export is not ready for download', { status: 400 })
    }

    // Parse options to get the original request data
    const options = exportRecord.options ? JSON.parse(exportRecord.options) : {}

    // Generate the PDF based on the export type with real data
    let pdfBlob: <PERSON>lo<PERSON>

    try {
      switch (exportRecord.type) {
        case 'quiz-result':
          // Fetch real quiz attempt data
          const attemptId = options.attemptId
          if (!attemptId) {
            return new NextResponse('Missing attempt ID for quiz result export', { status: 400 })
          }

          const attempt = await prisma.quizAttempt.findUnique({
            where: { id: attemptId },
            include: {
              quiz: {
                include: {
                  questions: {
                    orderBy: { order: 'asc' }
                  }
                }
              },
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          })

          if (!attempt) {
            return new NextResponse('Quiz attempt not found', { status: 404 })
          }

          const quizAttemptData: QuizAttemptData = {
            id: attempt.id,
            quiz: {
              id: attempt.quiz.id,
              title: attempt.quiz.title,
              description: attempt.quiz.description || '',
              questions: attempt.quiz.questions.map(question => ({
                id: question.id,
                text: question.text,
                type: question.type as 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY',
                options: question.options || undefined,
                correctAnswer: question.correctAnswer,
                explanation: question.explanation || undefined,
                points: question.points,
                order: question.order
              })),
              metadata: {
                totalPoints: attempt.totalPoints,
                estimatedDuration: attempt.quiz.timeLimit || 0,
                difficulty: attempt.quiz.difficulty || undefined,
                tags: attempt.quiz.tags || undefined
              },
              createdAt: attempt.quiz.createdAt.toISOString(),
              updatedAt: attempt.quiz.updatedAt.toISOString()
            },
            student: {
              id: attempt.user.id,
              name: attempt.user.name || 'Unknown',
              email: attempt.user.email || undefined
            },
            answers: attempt.answers as Record<string, string>,
            score: attempt.score,
            percentage: attempt.percentage,
            timeSpent: attempt.timeSpent || 0,
            startedAt: attempt.startedAt.toISOString(),
            completedAt: attempt.completedAt?.toISOString() || new Date().toISOString(),
            isCompleted: attempt.isCompleted
          }

          pdfBlob = await generateQuizResultPDF(quizAttemptData)
          break

        case 'analytics':
          // Fetch real analytics data for the user
          const userId = options.userId || exportRecord.userId

          const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
              quizAttempts: {
                where: { isCompleted: true },
                include: {
                  quiz: {
                    select: {
                      title: true,
                      difficulty: true,
                      tags: true
                    }
                  }
                },
                orderBy: { startedAt: 'desc' }
              }
            }
          })

          if (!user || user.quizAttempts.length === 0) {
            return new NextResponse('No analytics data found for user', { status: 404 })
          }

          // Calculate real analytics (similar to student analytics endpoint)
          const attempts = user.quizAttempts
          const totalAttempts = attempts.length
          const averageScore = attempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / totalAttempts
          const totalTimeSpent = attempts.reduce((sum, attempt) => sum + (attempt.timeSpent || 0), 0)

          const halfPoint = Math.floor(totalAttempts / 2)
          const firstHalfAvg = halfPoint > 0
            ? attempts.slice(-halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
            : 0
          const secondHalfAvg = halfPoint > 0
            ? attempts.slice(0, halfPoint).reduce((sum, attempt) => sum + attempt.percentage, 0) / halfPoint
            : 0
          const improvementRate = firstHalfAvg > 0 ? ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100 : 0

          const recentAttemptsData = attempts.slice(0, 10).map(attempt => ({
            quizTitle: attempt.quiz.title,
            score: attempt.score,
            percentage: attempt.percentage,
            completedAt: attempt.completedAt?.toISOString() || attempt.startedAt.toISOString()
          }))

          const analyticsData: AnalyticsData = {
            overview: {
              totalAttempts,
              averageScore,
              totalTimeSpent: Math.round(totalTimeSpent / 60),
              improvementRate
            },
            recentAttempts: recentAttemptsData
          }

          pdfBlob = await generateAnalyticsReportPDF(analyticsData, user.name || 'Unknown User')
          break

        case 'certificate':
          // For certificates, we'll need to implement a proper certificate generator
          // For now, return an error as the enhanced generator doesn't include certificates yet
          return new NextResponse('Certificate generation not yet implemented in enhanced generator', { status: 501 })

        case 'bulk':
          // For bulk exports, we'll need to implement bulk PDF generation
          return new NextResponse('Bulk export generation not yet implemented', { status: 501 })

        default:
          return new NextResponse('Unsupported export type', { status: 400 })
      }

      // Convert blob to buffer
      const buffer = Buffer.from(await pdfBlob.arrayBuffer())

      // Return the PDF file
      return new NextResponse(buffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${exportRecord.filename}"`,
          'Content-Length': buffer.length.toString()
        }
      })

    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError)
      return new NextResponse('Failed to generate PDF', { status: 500 })
    }

  } catch (error) {
    console.error('Error serving PDF file:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}
