import { toast } from "sonner"

// Track recent toasts to prevent duplicates
const recentToasts = new Set<string>()

/**
 * Show a toast with deduplication
 * Prevents the same toast from appearing multiple times within a short period
 */
export function showToast(
  type: 'success' | 'error' | 'info' | 'warning' | 'default',
  message: string,
  options?: {
    description?: string
    duration?: number
    deduplicationTime?: number
  }
) {
  const { description, duration = 4000, deduplicationTime = 3000 } = options || {}
  
  // Create a unique key for deduplication
  const toastKey = `${type}-${message}-${description || ''}`
  
  // Check if we've already shown this toast recently
  if (recentToasts.has(toastKey)) {
    return // Skip duplicate toast
  }
  
  // Add to recent toasts and remove after deduplication time
  recentToasts.add(toastKey)
  setTimeout(() => {
    recentToasts.delete(toastKey)
  }, deduplicationTime)
  
  // Show the toast
  const toastOptions = {
    description,
    duration
  }
  
  switch (type) {
    case 'success':
      return toast.success(message, toastOptions)
    case 'error':
      return toast.error(message, toastOptions)
    case 'warning':
      return toast.warning(message, toastOptions)
    case 'info':
      return toast.info(message, toastOptions)
    default:
      return toast(message, toastOptions)
  }
}

/**
 * Convenience functions for different toast types
 */
export const toastUtils = {
  success: (message: string, options?: { description?: string; duration?: number }) =>
    showToast('success', message, options),
    
  error: (message: string, options?: { description?: string; duration?: number }) =>
    showToast('error', message, options),
    
  warning: (message: string, options?: { description?: string; duration?: number }) =>
    showToast('warning', message, options),
    
  info: (message: string, options?: { description?: string; duration?: number }) =>
    showToast('info', message, options),
    
  default: (message: string, options?: { description?: string; duration?: number }) =>
    showToast('default', message, options),
}

/**
 * Clear all recent toast tracking
 * Useful for testing or when you want to reset the deduplication
 */
export function clearToastHistory() {
  recentToasts.clear()
}
