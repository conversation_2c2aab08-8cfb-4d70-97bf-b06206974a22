import { toast } from "sonner"

export interface APIError {
  message: string
  code?: string
  status: number
  details?: any
}

export class StudentAPIError extends <PERSON>rror {
  public status: number
  public code?: string
  public details?: any

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message)
    this.name = 'StudentAPIError'
    this.status = status
    this.code = code
    this.details = details
  }
}

/**
 * Centralized error handler for student-side API calls
 */
export class StudentErrorHandler {
  /**
   * Parse API response and throw appropriate error
   */
  static async handleAPIResponse(response: Response): Promise<any> {
    if (response.ok) {
      return await response.json()
    }

    let errorData: any = {}
    try {
      errorData = await response.json()
    } catch {
      // If JSON parsing fails, use status text
      errorData = { message: response.statusText }
    }

    const userFriendlyMessage = this.getUserFriendlyMessage(response.status, errorData)
    
    throw new StudentAPIError(
      userFriendlyMessage,
      response.status,
      errorData.code,
      errorData
    )
  }

  /**
   * Convert technical errors to user-friendly messages
   */
  static getUserFriendlyMessage(status: number, errorData: any): string {
    const message = errorData.message || errorData.error || 'An error occurred'
    const code = errorData.code

    // Handle specific error codes
    switch (code) {
      case 'MAX_ATTEMPTS_EXCEEDED':
        return 'You have reached the maximum number of attempts for this quiz.'
      case 'QUIZ_NOT_FOUND':
        return 'This quiz could not be found. It may have been removed or you may not have access to it.'
      case 'QUIZ_NOT_PUBLISHED':
        return 'This quiz is not currently available. Please check back later.'
      case 'QUIZ_EXPIRED':
        return 'This quiz has expired and is no longer available.'
      case 'ALREADY_ENROLLED':
        return 'You are already enrolled in this quiz.'
      case 'ENROLLMENT_REQUIRED':
        return 'You need to enroll in this quiz before taking it.'
      case 'ATTEMPT_NOT_FOUND':
        return 'Quiz attempt not found. Please start a new attempt.'
      case 'ATTEMPT_ALREADY_COMPLETED':
        return 'This quiz attempt has already been completed.'
      case 'UNAUTHORIZED':
        return 'You are not authorized to access this resource. Please log in again.'
      case 'FORBIDDEN':
        return 'You do not have permission to access this resource.'
    }

    // Handle by status code
    switch (status) {
      case 400:
        return message.includes('validation') 
          ? 'Please check your input and try again.'
          : message || 'Invalid request. Please check your input.'
      
      case 401:
        return 'Your session has expired. Please log in again.'
      
      case 403:
        return 'You do not have permission to perform this action.'
      
      case 404:
        return message.includes('quiz') 
          ? 'Quiz not found. It may have been removed or you may not have access to it.'
          : message.includes('result')
          ? 'Quiz result not found. Please check if the quiz was completed.'
          : 'The requested resource was not found.'
      
      case 409:
        return 'This action conflicts with the current state. Please refresh and try again.'
      
      case 429:
        return 'Too many requests. Please wait a moment before trying again.'
      
      case 500:
        return 'Server error. Please try again in a moment.'
      
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again later.'
      
      default:
        return message || 'An unexpected error occurred. Please try again.'
    }
  }

  /**
   * Handle errors with toast notifications and optional retry
   */
  static handleError(
    error: Error | StudentAPIError,
    context: string,
    options: {
      showToast?: boolean
      toastType?: 'error' | 'warning'
      retryCallback?: () => void
      fallbackMessage?: string
    } = {}
  ): string {
    const {
      showToast = true,
      toastType = 'error',
      retryCallback,
      fallbackMessage = 'An error occurred'
    } = options

    console.error(`${context}:`, error)

    let message: string
    if (error instanceof StudentAPIError) {
      message = error.message
    } else {
      message = error.message || fallbackMessage
    }

    if (showToast) {
      if (toastType === 'error') {
        toast.error(message, {
          action: retryCallback ? {
            label: 'Retry',
            onClick: retryCallback
          } : undefined
        })
      } else {
        toast.warning(message)
      }
    }

    return message
  }

  /**
   * Wrapper for fetch with automatic error handling
   */
  static async fetchWithErrorHandling(
    url: string,
    options: RequestInit = {},
    context: string = 'API call'
  ): Promise<any> {
    try {
      const response = await fetch(url, options)
      return await this.handleAPIResponse(response)
    } catch (error) {
      if (error instanceof StudentAPIError) {
        throw error
      }
      
      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new StudentAPIError(
          'Network error. Please check your internet connection.',
          0,
          'NETWORK_ERROR'
        )
      }
      
      throw new StudentAPIError(
        'An unexpected error occurred. Please try again.',
        500,
        'UNKNOWN_ERROR',
        error
      )
    }
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(error: StudentAPIError): boolean {
    return error.status >= 500 || error.status === 0 || error.code === 'NETWORK_ERROR'
  }

  /**
   * Get retry delay based on attempt count
   */
  static getRetryDelay(attemptCount: number): number {
    return Math.min(1000 * Math.pow(2, attemptCount), 10000) // Exponential backoff, max 10s
  }
}

/**
 * Hook for handling retries with exponential backoff
 */
export function useRetryableRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3
) {
  const executeWithRetry = async (
    onSuccess?: (data: T) => void,
    onError?: (error: StudentAPIError) => void
  ): Promise<T | null> => {
    let lastError: StudentAPIError | null = null
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await requestFn()
        onSuccess?.(result)
        return result
      } catch (error) {
        lastError = error instanceof StudentAPIError ? error : new StudentAPIError(
          error instanceof Error ? error.message : 'Unknown error',
          500
        )
        
        if (attempt < maxRetries && StudentErrorHandler.isRetryableError(lastError)) {
          const delay = StudentErrorHandler.getRetryDelay(attempt)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }
        
        break
      }
    }
    
    if (lastError) {
      onError?.(lastError)
      throw lastError
    }
    
    return null
  }

  return { executeWithRetry }
}
